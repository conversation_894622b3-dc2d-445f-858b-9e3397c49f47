#!/usr/bin/env python3
"""
Comprehensive test suite for the Quizolia API
This script tests all API endpoints to ensure they work correctly.
"""

import requests
import json
import sys
import time

# API base URL
BASE_URL = "http://localhost:5000"


def print_separator():
    """Print a visual separator"""
    print("=" * 60)


def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False


def test_root_endpoint():
    """Test the root endpoint"""
    print("\nTesting root endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Root endpoint passed")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
        return False


def test_swagger_docs():
    """Test the Swagger documentation endpoint"""
    print("\nTesting Swagger documentation...")
    try:
        response = requests.get(f"{BASE_URL}/swagger/")
        if response.status_code == 200:
            print("✅ Swagger documentation accessible")
            return True
        else:
            print(f"❌ Swagger documentation failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Swagger documentation error: {e}")
        return False


def test_user_signup():
    """Test user signup"""
    print("\nTesting user signup...")
    try:
        signup_data = {
            "firstname": "Test",
            "lastname": "User",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }

        response = requests.post(
            f"{BASE_URL}/api/auth/signup",
            json=signup_data,
            headers={"Content-Type": "application/json"}
        )

        # 201 for success, 409 if user already exists
        if response.status_code in [201, 409]:
            print("✅ User signup test passed")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ User signup failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ User signup error: {e}")
        return False


def test_user_login():
    """Test user login"""
    print("\nTesting user login...")
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }

        response = requests.post(
            f"{BASE_URL}/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            print("✅ User login test passed")
            print(f"Response: {response.json()}")

            # Extract cookies for further testing
            cookies = response.cookies
            return cookies
        else:
            print(f"❌ User login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ User login error: {e}")
        return None


def test_protected_endpoint(cookies):
    """Test a protected endpoint with authentication"""
    print("\nTesting protected endpoint (user profile)...")
    try:
        response = requests.get(
            f"{BASE_URL}/api/user/profile",
            cookies=cookies
        )

        if response.status_code == 200:
            print("✅ Protected endpoint test passed")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False


def test_quiz_endpoints(cookies):
    """Test quiz-related endpoints"""
    print("\nTesting quiz endpoints...")
    try:
        # Test quiz categories
        response = requests.get(
            f"{BASE_URL}/api/quiz/categories",
            cookies=cookies
        )

        if response.status_code == 200:
            print("✅ Quiz categories test passed")

            # Test random quiz
            response = requests.get(
                f"{BASE_URL}/api/quiz/random",
                cookies=cookies
            )

            if response.status_code == 200:
                print("✅ Random quiz test passed")
                return True
            else:
                print(f"❌ Random quiz failed: {response.status_code}")
                return False
        else:
            print(f"❌ Quiz categories failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Quiz endpoints error: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Quizolia API Tests\n")

    tests_passed = 0
    total_tests = 0

    # Basic endpoint tests
    total_tests += 1
    if test_health_check():
        tests_passed += 1

    total_tests += 1
    if test_root_endpoint():
        tests_passed += 1

    total_tests += 1
    if test_swagger_docs():
        tests_passed += 1

    # Authentication tests
    total_tests += 1
    if test_user_signup():
        tests_passed += 1

    total_tests += 1
    cookies = test_user_login()
    if cookies:
        tests_passed += 1

        # Protected endpoint tests (only if login successful)
        total_tests += 1
        if test_protected_endpoint(cookies):
            tests_passed += 1

        total_tests += 1
        if test_quiz_endpoints(cookies):
            tests_passed += 1

    # Summary
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All tests passed! The API is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the API implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
