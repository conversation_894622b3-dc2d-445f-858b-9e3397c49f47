# Quizolia API Documentation

## Overview

Quizolia API is a comprehensive quiz application backend built with Flask, featuring JWT-based cookie authentication, Swagger documentation, and a RESTful API design. The API provides secure user authentication, quiz management, and user profile functionality.

## Project Structure

```
quizolia/
├── app.py                 # Main Flask application
├── models.py              # Database models
├── config.py              # Configuration settings
├── run_api.py             # Startup script
├── test_api.py            # Test suite
├── requirements.txt       # Python dependencies
├── .env                   # Environment variables
├── venv/                  # Virtual environment
├── quizolia_old/          # Old template-based files
│   ├── app.py             # Original Flask app
│   ├── templates/         # HTML templates
│   ├── static/            # CSS, JS, images
│   └── ...                # Other legacy files
└── README.md              # This file
```

## Features

- 🔐 **JWT Cookie Authentication** - Secure authentication with HTTP-only cookies
- 📚 **Swagger Documentation** - Interactive API documentation
- 🔄 **Automatic Token Refresh** - Seamless token renewal
- 🛡️ **CORS Support** - Cross-origin resource sharing enabled
- 📊 **RESTful Design** - Clean and consistent API endpoints
- 🗄️ **Database Integration** - SQLAlchemy ORM with MySQL support

## Quick Start

### Prerequisites

- Python 3.8+
- MySQL database
- Virtual environment (recommended)

### Installation

1. **Clone the repository and navigate to the project directory**

   ```bash
   cd /path/to/quizolia
   ```

2. **Activate the virtual environment**

   ```bash
   source venv/bin/activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   Create a `.env` file with:

   ```env
   SQLALCHEMY_DATABASE_URI=mysql://username:password@localhost/quizolia_db
   SECRET_KEY=your-secret-key-here
   FLASK_ENV=development
   ```

5. **Start the API server**

   ```bash
   python run_api.py
   ```

6. **Access the API**
   - API Base URL: `http://localhost:5000`
   - Swagger Documentation: `http://localhost:5000/swagger/`
   - Health Check: `http://localhost:5000/health`

## API Endpoints

### Authentication

| Method | Endpoint            | Description                   |
| ------ | ------------------- | ----------------------------- |
| POST   | `/api/auth/signup`  | Register a new user           |
| POST   | `/api/auth/login`   | Login user (sets JWT cookies) |
| POST   | `/api/auth/logout`  | Logout user (clears cookies)  |
| POST   | `/api/auth/refresh` | Refresh access token          |
| GET    | `/api/auth/verify`  | Verify current token          |

### User Management

| Method | Endpoint              | Description             |
| ------ | --------------------- | ----------------------- |
| GET    | `/api/user/profile`   | Get user profile        |
| PUT    | `/api/user/profile`   | Update user profile     |
| GET    | `/api/user/dashboard` | Get dashboard data      |
| GET    | `/api/user/quizzes`   | Get user's quiz history |

### Quiz System

| Method | Endpoint               | Description             |
| ------ | ---------------------- | ----------------------- |
| GET    | `/api/quiz/categories` | Get quiz categories     |
| GET    | `/api/quiz/random`     | Get random quiz         |
| GET    | `/api/quiz/multi`      | Get multi-category quiz |

### Leaderboard

| Method | Endpoint           | Description            |
| ------ | ------------------ | ---------------------- |
| GET    | `/api/leaderboard` | Get global leaderboard |

## Authentication Flow

### JWT Cookie-Based Authentication

The API uses a secure JWT cookie-based authentication system:

1. **Login**: User provides credentials, receives JWT tokens in HTTP-only cookies
2. **Access**: Short-lived access token (15 minutes) for API requests
3. **Refresh**: Long-lived refresh token (30 days) for token renewal
4. **Middleware**: Automatic token verification on protected routes

### Cookie Configuration

- **HTTP-Only**: Prevents XSS attacks
- **SameSite**: CSRF protection
- **Secure**: HTTPS-only in production

## Request/Response Format

### Standard Response Format

```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data here
  }
}
```

### Error Response Format

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

## Example Usage

### User Registration

```bash
curl -X POST http://localhost:5000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "firstname": "John",
    "lastname": "Doe",
    "email": "<EMAIL>",
    "password": "securepassword"
  }'
```

### User Login

```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -c cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword"
  }'
```

### Access Protected Endpoint

```bash
curl -X GET http://localhost:5000/api/user/profile \
  -b cookies.txt
```

## Testing

Run the test suite to verify API functionality:

```bash
python test_api.py
```

The test script will:

- Test basic endpoints (health, root, swagger)
- Test user registration and login
- Test protected endpoints with authentication
- Test quiz-related endpoints

## Development

### Project Structure

```
quizolia/
├── api_app.py          # Main Flask application
├── run_api.py          # Startup script
├── test_api.py         # Test suite
├── requirements.txt    # Python dependencies
├── .env               # Environment variables
└── venv/              # Virtual environment
```

### Key Components

- **Flask-RESTX**: API framework with Swagger integration
- **SQLAlchemy**: Database ORM
- **PyJWT**: JWT token handling
- **Flask-CORS**: Cross-origin support
- **Werkzeug**: Password hashing

## Security Features

- **Password Hashing**: Werkzeug secure password hashing
- **JWT Tokens**: Signed with secret key
- **HTTP-Only Cookies**: XSS protection
- **CORS Configuration**: Controlled cross-origin access
- **Token Expiration**: Automatic token lifecycle management

## Production Deployment

For production deployment:

1. Set `FLASK_ENV=production`
2. Use HTTPS and set `secure=True` for cookies
3. Configure proper CORS origins
4. Use a production WSGI server (e.g., Gunicorn)
5. Set up proper logging and monitoring

## Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation
4. Ensure security best practices

## License

This project is part of the Quizolia application suite.
