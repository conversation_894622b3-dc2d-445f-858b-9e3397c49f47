{"data_mtime": 1748639575, "dep_lines": [10, 6, 7, 11, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 9], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["werkzeug.security", "flask", "flask_sqlalchemy", "functools", "jwt", "os", "dotenv", "datetime", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "dotenv.main", "flask.app", "flask.config", "flask.globals", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "flask.wrappers", "flask_sqlalchemy.extension", "flask_sqlalchemy.query", "sqlalchemy", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm", "sqlalchemy.orm.query", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "typing", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.request", "wsgiref", "wsgiref.types"], "hash": "36591980b59b370e5f1210a5e528da45cf33594f", "id": "app", "ignore_all": true, "interface_hash": "f575a2839331815c2d25ade5602d6eb7155a6908", "mtime": 1748639513, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/app.py", "plugin_data": null, "size": 18423, "suppressed": ["flask_cors", "flask_restx"], "version_id": "1.15.0"}