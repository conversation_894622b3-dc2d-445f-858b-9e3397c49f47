{".class": "MypyFile", "_fullname": "config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.Config", "name": "Config", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.Config", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.Config", "builtins.object"], "names": {".class": "SymbolTable", "API_DESCRIPTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.Config.API_DESCRIPTION", "name": "API_DESCRIPTION", "type": "builtins.str"}}, "API_TITLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.Config.API_TITLE", "name": "API_TITLE", "type": "builtins.str"}}, "API_VERSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.Config.API_VERSION", "name": "API_VERSION", "type": "builtins.str"}}, "CORS_ORIGINS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.CORS_ORIGINS", "name": "CORS_ORIGINS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "JWT_ACCESS_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.JWT_ACCESS_TOKEN_EXPIRES", "name": "JWT_ACCESS_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "JWT_REFRESH_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.JWT_REFRESH_TOKEN_EXPIRES", "name": "JWT_REFRESH_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.SECRET_KEY", "name": "SECRET_KEY", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "SQLALCHEMY_DATABASE_URI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.SQLALCHEMY_DATABASE_URI", "name": "SQLALCHEMY_DATABASE_URI", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "SQLALCHEMY_TRACK_MODIFICATIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.Config.SQLALCHEMY_TRACK_MODIFICATIONS", "name": "SQLALCHEMY_TRACK_MODIFICATIONS", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.Config.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.Config", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DevelopmentConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.DevelopmentConfig", "name": "DevelopmentConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.DevelopmentConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.DevelopmentConfig", "config.Config", "builtins.object"], "names": {".class": "SymbolTable", "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.DevelopmentConfig.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "FLASK_ENV": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.DevelopmentConfig.FLASK_ENV", "name": "FLASK_ENV", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.DevelopmentConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.DevelopmentConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProductionConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.ProductionConfig", "name": "ProductionConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.ProductionConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.ProductionConfig", "config.Config", "builtins.object"], "names": {".class": "SymbolTable", "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "FLASK_ENV": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.FLASK_ENV", "name": "FLASK_ENV", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.ProductionConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.ProductionConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestingConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.TestingConfig", "name": "TestingConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.TestingConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.TestingConfig", "config.Config", "builtins.object"], "names": {".class": "SymbolTable", "SQLALCHEMY_DATABASE_URI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.TestingConfig.SQLALCHEMY_DATABASE_URI", "name": "SQLALCHEMY_DATABASE_URI", "type": "builtins.str"}}, "TESTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.TestingConfig.TESTING", "name": "TESTING", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.TestingConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.TestingConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "config.config", "name": "config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["config.DevelopmentConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "load_dotenv": {".class": "SymbolTableNode", "cross_ref": "dotenv.main.load_dotenv", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/config.py"}