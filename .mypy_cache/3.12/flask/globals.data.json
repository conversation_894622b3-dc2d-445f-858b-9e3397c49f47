{".class": "MypyFile", "_fullname": "flask.globals", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AppContext": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.AppContext", "kind": "Gdef"}, "ContextVar": {".class": "SymbolTableNode", "cross_ref": "_contextvars.ContextVar", "kind": "Gdef"}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "LocalProxy": {".class": "SymbolTableNode", "cross_ref": "werkzeug.local.LocalProxy", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "flask.wrappers.Request", "kind": "Gdef"}, "RequestContext": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.RequestContext", "kind": "Gdef"}, "SessionMixin": {".class": "SymbolTableNode", "cross_ref": "flask.sessions.SessionMixin", "kind": "Gdef"}, "_AppCtxGlobals": {".class": "SymbolTableNode", "cross_ref": "flask.ctx._AppCtxGlobals", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.globals.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.globals.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.globals.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.globals.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.globals.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.globals.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cv_app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals._cv_app", "name": "_cv_app", "type": {".class": "Instance", "args": ["flask.ctx.AppContext"], "extra_attrs": null, "type_ref": "_contextvars.ContextVar"}}}, "_cv_request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals._cv_request", "name": "_cv_request", "type": {".class": "Instance", "args": ["flask.ctx.RequestContext"], "extra_attrs": null, "type_ref": "_contextvars.ContextVar"}}}, "_no_app_msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "flask.globals._no_app_msg", "name": "_no_app_msg", "type": "builtins.str"}}, "_no_req_msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "flask.globals._no_req_msg", "name": "_no_req_msg", "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "app_ctx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals.app_ctx", "name": "app_ctx", "type": "flask.ctx.AppContext"}}, "current_app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals.current_app", "name": "current_app", "type": "flask.app.Flask"}}, "g": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals.g", "name": "g", "type": "flask.ctx._AppCtxGlobals"}}, "request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals.request", "name": "request", "type": "flask.wrappers.Request"}}, "request_ctx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals.request_ctx", "name": "request_ctx", "type": "flask.ctx.RequestContext"}}, "session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "flask.globals.session", "name": "session", "type": "flask.sessions.SessionMixin"}}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask/globals.py"}