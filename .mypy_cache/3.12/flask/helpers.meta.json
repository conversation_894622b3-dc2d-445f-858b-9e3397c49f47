{"data_mtime": 1748629307, "dep_lines": [3, 11, 12, 14, 16, 21, 24, 1, 3, 4, 5, 6, 7, 8, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 25, 5, 20, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["importlib.util", "werkzeug.utils", "werkzeug.exceptions", "werkzeug.wrappers", "flask.globals", "flask.signals", "flask.wrappers", "__future__", "importlib", "os", "sys", "typing", "datetime", "functools", "werkzeug", "builtins", "_frozen_importlib", "abc", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers.response"], "hash": "17709c8247dda74292b6fe6ab8b65d253e7118ad", "id": "flask.helpers", "ignore_all": true, "interface_hash": "64222eff3160cb9999779f24260511d41c15ff39", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask/helpers.py", "plugin_data": null, "size": 23521, "suppressed": [], "version_id": "1.15.0"}