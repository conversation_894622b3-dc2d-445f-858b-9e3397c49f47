{"data_mtime": 1748629307, "dep_lines": [4, 5, 18, 20, 21, 23, 24, 30, 34, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 818], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 25, 25, 5, 10, 20, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["collections.abc", "importlib.metadata", "click.core", "werkzeug.serving", "werkzeug.utils", "flask.globals", "flask.helpers", "_typeshed.wsgi", "flask.app", "__future__", "ast", "collections", "importlib", "inspect", "os", "platform", "re", "sys", "traceback", "typing", "functools", "operator", "types", "click", "werkzeug", "ssl", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "click.decorators", "click.exceptions", "click.types", "enum", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold"], "hash": "9a94e72e371871c7ff31d3e3f100478281038e75", "id": "flask.cli", "ignore_all": true, "interface_hash": "7be70840dce1ff2d63fb16435b2fa8c6435b5dfd", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask/cli.py", "plugin_data": null, "size": 37184, "suppressed": ["cryptography"], "version_id": "1.15.0"}