{"data_mtime": 1748629307, "dep_lines": [20, 21, 10, 14, 15, 19, 83, 1, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 5, 5, 25, 20, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask.sansio.app", "flask.sansio.scaffold", "flask.globals", "flask.helpers", "flask.signals", "flask.app", "flask.debughelpers", "__future__", "typing", "jinja2", "builtins", "_frozen_importlib", "abc", "flask.sansio", "jinja2.bccache", "jinja2.environment", "jinja2.ext", "jinja2.loaders", "jinja2.runtime"], "hash": "c2dd054203b2f946ae29a13941da1a7136999ee9", "id": "flask.templating", "ignore_all": true, "interface_hash": "b0289894092523106e25b47b32b68f9738ba8d97", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask/templating.py", "plugin_data": null, "size": 7537, "suppressed": [], "version_id": "1.15.0"}