{"data_mtime": 1748629307, "dep_lines": [3, 13, 15, 17, 18, 19, 1, 3, 4, 5, 6, 7, 8, 9, 11, 17, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 5, 5, 20, 10, 10, 10, 10, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.util", "werkzeug.exceptions", "werkzeug.utils", "flask.typing", "flask.helpers", "flask.templating", "__future__", "importlib", "os", "pathlib", "sys", "typing", "collections", "functools", "jinja2", "flask", "click", "builtins", "_frozen_importlib", "_typeshed", "abc", "click.core", "jinja2.loaders", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response", "wsgiref", "wsgiref.types"], "hash": "ebfc1f2ac72875c2a52c46be8ac7104f4f8abecc", "id": "flask.sansio.scaffold", "ignore_all": true, "interface_hash": "d83d74345b099a6e2a3f3c59559a6c2b37eef106", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask/sansio/scaffold.py", "plugin_data": null, "size": 30387, "suppressed": [], "version_id": "1.15.0"}