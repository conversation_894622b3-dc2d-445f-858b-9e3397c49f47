{"data_mtime": 1748629307, "dep_lines": [14, 3, 12, 19, 20, 1, 3, 4, 5, 7, 10, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 5, 20, 10, 10, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask.json.tag", "collections.abc", "werkzeug.datastructures", "flask.app", "flask.wrappers", "__future__", "collections", "<PERSON><PERSON><PERSON>", "typing", "datetime", "itsdangerous", "typing_extensions", "builtins", "_frozen_importlib", "abc", "flask.json", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "itsdangerous.serializer", "itsdangerous.timed", "itsdangerous.url_safe", "werkzeug", "werkzeug.datastructures.mixins", "werkzeug.datastructures.structures", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.request", "werkzeug.wrappers.response"], "hash": "dd0a20b00552578a331b0005e33566cb2e80dde6", "id": "flask.sessions", "ignore_all": true, "interface_hash": "54375c3902597ea19329f60b906c8a59a3d6c011", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask/sessions.py", "plugin_data": null, "size": 15495, "suppressed": [], "version_id": "1.15.0"}