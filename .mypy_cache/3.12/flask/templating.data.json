{".class": "MypyFile", "_fullname": "flask.templating", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "App": {".class": "SymbolTableNode", "cross_ref": "flask.sansio.app.App", "kind": "Gdef"}, "BaseEnvironment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "BaseLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.BaseLoader", "kind": "Gdef"}, "DispatchingJinjaLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.loaders.BaseLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask.templating.DispatchingJinjaLoader", "name": "DispatchingJinjaL<PERSON>der", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask.templating.DispatchingJinjaLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask.templating", "mro": ["flask.templating.DispatchingJinjaLoader", "jinja2.loaders.BaseLoader", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.DispatchingJinjaLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["flask.templating.DispatchingJinjaLoader", "flask.sansio.app.App"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DispatchingJinjaLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_source_explained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "environment", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.DispatchingJinjaLoader._get_source_explained", "name": "_get_source_explained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "environment", "template"], "arg_types": ["flask.templating.DispatchingJinjaLoader", "jinja2.environment.Environment", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_source_explained of DispatchingJinjaLoader", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_source_fast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "environment", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.DispatchingJinjaLoader._get_source_fast", "name": "_get_source_fast", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "environment", "template"], "arg_types": ["flask.templating.DispatchingJinjaLoader", "jinja2.environment.Environment", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_source_fast of DispatchingJinjaLoader", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iter_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.DispatchingJinjaLoader._iter_loaders", "name": "_iter_loaders", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "template"], "arg_types": ["flask.templating.DispatchingJinjaLoader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iter_loaders of DispatchingJinjaLoader", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["flask.sansio.scaffold.Scaffold", "jinja2.loaders.BaseLoader"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask.templating.DispatchingJinjaLoader.app", "name": "app", "type": "flask.sansio.app.App"}}, "get_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "environment", "template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.DispatchingJinjaLoader.get_source", "name": "get_source", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "environment", "template"], "arg_types": ["flask.templating.DispatchingJinjaLoader", "jinja2.environment.Environment", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_source of DispatchingJinjaLoader", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_templates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.DispatchingJinjaLoader.list_templates", "name": "list_templates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask.templating.DispatchingJinjaLoader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_templates of DispatchingJinjaLoader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask.templating.DispatchingJinjaLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask.templating.DispatchingJinjaLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Environment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.environment.Environment"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask.templating.Environment", "name": "Environment", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask.templating.Environment", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask.templating", "mro": ["flask.templating.Environment", "jinja2.environment.Environment", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "app", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.Environment.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "app", "options"], "arg_types": ["flask.templating.Environment", "flask.sansio.app.App", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Environment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask.templating.Environment.app", "name": "app", "type": "flask.sansio.app.App"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask.templating.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask.templating.Environment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "Scaffold": {".class": "SymbolTableNode", "cross_ref": "flask.sansio.scaffold.Scaffold", "kind": "Gdef"}, "Template": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Template", "kind": "Gdef"}, "TemplateNotFound": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateNotFound", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.templating.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.templating.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.templating.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.templating.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.templating.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.templating.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cv_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals._cv_app", "kind": "Gdef"}, "_cv_request": {".class": "SymbolTableNode", "cross_ref": "flask.globals._cv_request", "kind": "Gdef"}, "_default_template_ctx_processor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating._default_template_ctx_processor", "name": "_default_template_ctx_processor", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_template_ctx_processor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["app", "template", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating._render", "name": "_render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["app", "template", "context"], "arg_types": ["flask.app.Flask", "jinja2.environment.Template", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["app", "template", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating._stream", "name": "_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["app", "template", "context"], "arg_types": ["flask.app.Flask", "jinja2.environment.Template", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_stream", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "before_render_template": {".class": "SymbolTableNode", "cross_ref": "flask.signals.before_render_template", "kind": "Gdef"}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "render_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["template_name_or_list", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.render_template", "name": "render_template", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["template_name_or_list", "context"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "jinja2.environment.Template", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "jinja2.environment.Template"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_template", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_template_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["source", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.render_template_string", "name": "render_template_string", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["source", "context"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_template_string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "stream_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["template_name_or_list", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.stream_template", "name": "stream_template", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["template_name_or_list", "context"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "jinja2.environment.Template", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "jinja2.environment.Template"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_template", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream_template_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["source", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.templating.stream_template_string", "name": "stream_template_string", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["source", "context"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_template_string", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream_with_context": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.stream_with_context", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "template_rendered": {".class": "SymbolTableNode", "cross_ref": "flask.signals.template_rendered", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask/templating.py"}