{"data_mtime": 1748629307, "dep_lines": [7, 1, 3, 4, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm", "__future__", "typing", "math", "sqlalchemy", "flask", "builtins", "_frozen_importlib", "abc", "flask.helpers", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response"], "hash": "9d0a0808fc800b8af0d3e85b4a13a8ed4e81dfde", "id": "flask_sqlalchemy.pagination", "ignore_all": true, "interface_hash": "f938b31c43ed26f36dc94b36c3c19f10751a16cd", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/pagination.py", "plugin_data": null, "size": 11119, "suppressed": [], "version_id": "1.15.0"}