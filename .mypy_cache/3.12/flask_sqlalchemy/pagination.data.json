{".class": "MypyFile", "_fullname": "flask_sqlalchemy.pagination", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Pagination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.pagination.Pagination", "name": "Pagination", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.pagination", "mro": ["flask_sqlalchemy.pagination.Pagination", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "page", "per_page", "max_per_page", "error_out", "count", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "page", "per_page", "max_per_page", "error_out", "count", "kwargs"], "arg_types": ["flask_sqlalchemy.pagination.Pagination", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Pagination", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of Pagination", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_page_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["page", "per_page", "max_per_page", "error_out"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination._prepare_page_args", "name": "_prepare_page_args", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["page", "per_page", "max_per_page", "error_out"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_page_args of Pagination", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination._prepare_page_args", "name": "_prepare_page_args", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["page", "per_page", "max_per_page", "error_out"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_page_args of Pagination", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_query_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination._query_args", "name": "_query_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_query_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination._query_count", "name": "_query_count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_count of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_query_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination._query_items", "name": "_query_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_items of Pagination", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_query_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination._query_offset", "name": "_query_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_offset of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination._query_offset", "name": "_query_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_offset of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "first": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination.first", "name": "first", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "first of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.first", "name": "first", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "first of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination.has_next", "name": "has_next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_next of Pagination", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.has_next", "name": "has_next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_next of Pagination", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_prev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination.has_prev", "name": "has_prev", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_prev of Pagination", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.has_prev", "name": "has_prev", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_prev of Pagination", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.items", "name": "items", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "iter_pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "left_edge", "left_current", "right_current", "right_edge"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination.iter_pages", "name": "iter_pages", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "left_edge", "left_current", "right_current", "right_edge"], "arg_types": ["flask_sqlalchemy.pagination.Pagination", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_pages of Pagination", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination.last", "name": "last", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.last", "name": "last", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "max_per_page": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.max_per_page", "name": "max_per_page", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "error_out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination.next", "name": "next", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "error_out"], "arg_types": ["flask_sqlalchemy.pagination.Pagination", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next of Pagination", "ret_type": "flask_sqlalchemy.pagination.Pagination", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination.next_num", "name": "next_num", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_num of Pagination", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.next_num", "name": "next_num", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_num of Pagination", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "page": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.page", "name": "page", "type": "builtins.int"}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pages of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pages of Pagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "per_page": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.per_page", "name": "per_page", "type": "builtins.int"}}, "prev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "error_out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.Pagination.prev", "name": "prev", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "error_out"], "arg_types": ["flask_sqlalchemy.pagination.Pagination", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prev of Pagination", "ret_type": "flask_sqlalchemy.pagination.Pagination", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prev_num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.pagination.Pagination.prev_num", "name": "prev_num", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prev_num of Pagination", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.prev_num", "name": "prev_num", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.Pagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prev_num of Pagination", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "total": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.pagination.Pagination.total", "name": "total", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.pagination.Pagination.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.pagination.Pagination", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QueryPagination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flask_sqlalchemy.pagination.Pagination"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.pagination.QueryPagination", "name": "QueryPagination", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.QueryPagination", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.pagination", "mro": ["flask_sqlalchemy.pagination.QueryPagination", "flask_sqlalchemy.pagination.Pagination", "builtins.object"], "names": {".class": "SymbolTable", "_query_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.QueryPagination._query_count", "name": "_query_count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.QueryPagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_count of QueryPagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_query_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.QueryPagination._query_items", "name": "_query_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.QueryPagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_items of QueryPagination", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.pagination.QueryPagination.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.pagination.QueryPagination", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectPagination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flask_sqlalchemy.pagination.Pagination"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.pagination.SelectPagination", "name": "SelectPagination", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.SelectPagination", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.pagination", "mro": ["flask_sqlalchemy.pagination.SelectPagination", "flask_sqlalchemy.pagination.Pagination", "builtins.object"], "names": {".class": "SymbolTable", "_query_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.SelectPagination._query_count", "name": "_query_count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.SelectPagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_count of SelectPagination", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_query_items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.pagination.SelectPagination._query_items", "name": "_query_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.pagination.SelectPagination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_query_items of SelectPagination", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.pagination.SelectPagination.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.pagination.SelectPagination", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.pagination.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.pagination.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.pagination.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.pagination.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.pagination.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.pagination.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abort": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.abort", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "ceil": {".class": "SymbolTableNode", "cross_ref": "math.ceil", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "sa": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy", "kind": "Gdef"}, "sa_orm": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/pagination.py"}