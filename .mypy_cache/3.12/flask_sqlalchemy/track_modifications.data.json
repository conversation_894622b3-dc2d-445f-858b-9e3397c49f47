{".class": "MypyFile", "_fullname": "flask_sqlalchemy.track_modifications", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Namespace": {".class": "SymbolTableNode", "cross_ref": "blinker.base.Namespace", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.session.Session", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.track_modifications.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.track_modifications.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.track_modifications.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.track_modifications.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.track_modifications.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.track_modifications.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_after_commit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.track_modifications._after_commit", "name": "_after_commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["flask_sqlalchemy.session.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_after_commit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_after_rollback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.track_modifications._after_rollback", "name": "_after_rollback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["flask_sqlalchemy.session.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_after_rollback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_before_commit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.track_modifications._before_commit", "name": "_before_commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["flask_sqlalchemy.session.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_before_commit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_listen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.track_modifications._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": [{".class": "Instance", "args": ["flask_sqlalchemy.session.Session"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.scoping.scoped_session"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_record_ops": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["session", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.track_modifications._record_ops", "name": "_record_ops", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["session", "kwargs"], "arg_types": ["flask_sqlalchemy.session.Session", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_record_ops", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_signals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask_sqlalchemy.track_modifications._signals", "name": "_signals", "type": "blinker.base.Namespace"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "before_models_committed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask_sqlalchemy.track_modifications.before_models_committed", "name": "before_models_committed", "type": "blinker.base.NamedSignal"}}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "has_app_context": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.has_app_context", "kind": "Gdef"}, "models_committed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask_sqlalchemy.track_modifications.models_committed", "name": "models_committed", "type": "blinker.base.NamedSignal"}}, "sa": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy", "kind": "Gdef"}, "sa_event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "sa_orm": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/track_modifications.py"}