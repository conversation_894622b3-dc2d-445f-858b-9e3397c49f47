{".class": "MypyFile", "_fullname": "flask_sqlalchemy.extension", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BindMixin": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.model.BindMixin", "kind": "Gdef"}, "DefaultMeta": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.model.DefaultMeta", "kind": "Gdef"}, "DefaultMetaNoName": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.model.DefaultMetaNoName", "kind": "Gdef"}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.model.Model", "kind": "Gdef"}, "NameMixin": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.model.NameMixin", "kind": "Gdef"}, "Pagination": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.pagination.Pagination", "kind": "Gdef"}, "Query": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.query.Query", "kind": "Gdef"}, "SQLAlchemy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.extension.SQLAlchemy", "name": "SQLAlchemy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.extension", "mro": ["flask_sqlalchemy.extension.SQLAlchemy", "builtins.object"], "names": {".class": "SymbolTable", "Model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.Model", "name": "Model", "type": {".class": "TypeType", "item": "flask_sqlalchemy.extension._FSAModel"}}}, "Query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.Query", "name": "Query", "type": {".class": "TypeType", "item": "flask_sqlalchemy.query.Query"}}}, "Table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.Table", "name": "Table", "type": {".class": "TypeType", "item": "flask_sqlalchemy.table._Table"}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of SQLAlchemy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "app", "metadata", "session_options", "query_class", "model_class", "engine_options", "add_models_to_shell", "disable_autonaming"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "app", "metadata", "session_options", "query_class", "model_class", "engine_options", "add_models_to_shell", "disable_autonaming"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["flask.app.Flask", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "flask_sqlalchemy.query.Query"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSA_MCT", "id": -1, "name": "_FSA_MCT", "namespace": "flask_sqlalchemy.extension.SQLAlchemy.__init__", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}, "sqlalchemy.orm.decl_api.DeclarativeMeta", {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSA_MCT", "id": -1, "name": "_FSA_MCT", "namespace": "flask_sqlalchemy.extension.SQLAlchemy.__init__", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}, "sqlalchemy.orm.decl_api.DeclarativeMeta", {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of SQLAlchemy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_models_to_shell": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._add_models_to_shell", "name": "_add_models_to_shell", "type": "builtins.bool"}}, "_app_engines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._app_engines", "name": "_app_engines", "type": {".class": "Instance", "args": ["flask.app.Flask", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "sqlalchemy.engine.base.Engine"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "weakref.<PERSON>eyDictionary"}}}, "_apply_driver_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "options", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._apply_driver_defaults", "name": "_apply_driver_defaults", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "options", "app"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "flask.app.Flask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_driver_defaults of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_call_for_binds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bind_key", "op_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._call_for_binds", "name": "_call_for_binds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bind_key", "op_name"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_call_for_binds of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_engine_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._engine_options", "name": "_engine_options", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_make_declarative_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_class", "disable_autonaming"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._make_declarative_base", "name": "_make_declarative_base", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_class", "disable_autonaming"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSA_MCT", "id": -1, "name": "_FSA_MCT", "namespace": "flask_sqlalchemy.extension.SQLAlchemy._make_declarative_base", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}, "sqlalchemy.orm.decl_api.DeclarativeMeta", {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_declarative_base of SQLAlchemy", "ret_type": {".class": "TypeType", "item": "flask_sqlalchemy.extension._FSAModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSA_MCT", "id": -1, "name": "_FSA_MCT", "namespace": "flask_sqlalchemy.extension.SQLAlchemy._make_declarative_base", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}, "sqlalchemy.orm.decl_api.DeclarativeMeta", {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "_make_engine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "bind_key", "options", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._make_engine", "name": "_make_engine", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "bind_key", "options", "app"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "flask.app.Flask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_engine of SQLAlchemy", "ret_type": "sqlalchemy.engine.base.Engine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bind_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._make_metadata", "name": "_make_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bind_key"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_metadata of SQLAlchemy", "ret_type": "sqlalchemy.sql.schema.MetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_scoped_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._make_scoped_session", "name": "_make_scoped_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_scoped_session of SQLAlchemy", "ret_type": {".class": "Instance", "args": ["flask_sqlalchemy.session.Session"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.scoping.scoped_session"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_session_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._make_session_factory", "name": "_make_session_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_session_factory of SQLAlchemy", "ret_type": {".class": "Instance", "args": ["flask_sqlalchemy.session.Session"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.session.sessionmaker"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_table_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._make_table_class", "name": "_make_table_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_table_class of SQLAlchemy", "ret_type": {".class": "TypeType", "item": "flask_sqlalchemy.table._Table"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_relation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._relation", "name": "_relation", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_relation of SQLAlchemy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships.RelationshipProperty"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_rel_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._set_rel_query", "name": "_set_rel_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_rel_query of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_teardown_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy._teardown_session", "name": "_teardown_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_teardown_session of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "bind_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.create_all", "name": "create_all", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "bind_key"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_all of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "bind_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.drop_all", "name": "drop_all", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "bind_key"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_all of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dynamic_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "argument", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.dynamic_loader", "name": "dynamic_loader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "argument", "kwargs"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dynamic_loader of SQLAlchemy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships.RelationshipProperty"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "engine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.engine", "name": "engine", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engine of SQLAlchemy", "ret_type": "sqlalchemy.engine.base.Engine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.engine", "name": "engine", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engine of SQLAlchemy", "ret_type": "sqlalchemy.engine.base.Engine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "engines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.engines", "name": "engines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engines of SQLAlchemy", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "sqlalchemy.engine.base.Engine"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.engines", "name": "engines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "engines of SQLAlchemy", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "sqlalchemy.engine.base.Engine"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "first_or_404": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "statement", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.first_or_404", "name": "first_or_404", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "statement", "description"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "first_or_404 of SQLAlchemy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_engine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "bind_key", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.get_engine", "name": "get_engine", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "bind_key", "kwargs"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_engine of SQLAlchemy", "ret_type": "sqlalchemy.engine.base.Engine", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_or_404": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "entity", "ident", "description", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.get_or_404", "name": "get_or_404", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "entity", "ident", "description", "kwargs"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._O", "id": -1, "name": "_O", "namespace": "flask_sqlalchemy.extension.SQLAlchemy.get_or_404", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_or_404 of SQLAlchemy", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._O", "id": -1, "name": "_O", "namespace": "flask_sqlalchemy.extension.SQLAlchemy.get_or_404", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._O", "id": -1, "name": "_O", "namespace": "flask_sqlalchemy.extension.SQLAlchemy.get_or_404", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "init_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.init_app", "name": "init_app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", "flask.app.Flask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_app of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.metadata", "name": "metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata of SQLAlchemy", "ret_type": "sqlalchemy.sql.schema.MetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.metadata", "name": "metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata of SQLAlchemy", "ret_type": "sqlalchemy.sql.schema.MetaData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "metadatas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.metadatas", "name": "metadatas", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "sqlalchemy.sql.schema.MetaData"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "one_or_404": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "statement", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.one_or_404", "name": "one_or_404", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "statement", "description"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "one_or_404 of SQLAlchemy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "paginate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "select", "page", "per_page", "max_per_page", "error_out", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.paginate", "name": "paginate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "select", "page", "per_page", "max_per_page", "error_out", "count"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "paginate of SQLAlchemy", "ret_type": "flask_sqlalchemy.pagination.Pagination", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reflect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "bind_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.reflect", "name": "reflect", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "bind_key"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reflect of SQLAlchemy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "relationship": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.relationship", "name": "relationship", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["flask_sqlalchemy.extension.SQLAlchemy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "relationship of SQLAlchemy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships.RelationshipProperty"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask_sqlalchemy.extension.SQLAlchemy.session", "name": "session", "type": {".class": "Instance", "args": ["flask_sqlalchemy.session.Session"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.scoping.scoped_session"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension.SQLAlchemy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.extension.SQLAlchemy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectPagination": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.pagination.SelectPagination", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.session.Session", "kind": "Gdef"}, "WeakKeyDictionary": {".class": "SymbolTableNode", "cross_ref": "weakref.<PERSON>eyDictionary", "kind": "Gdef"}, "_FSAModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flask_sqlalchemy.model.Model"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.extension._FSAModel", "name": "_FSAModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension._FSAModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.extension", "mro": ["flask_sqlalchemy.extension._FSAModel", "flask_sqlalchemy.model.Model", "builtins.object"], "names": {".class": "SymbolTable", "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.extension._FSAModel.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSAModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.extension._FSAModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FSA_MCT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSA_MCT", "name": "_FSA_MCT", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}, "sqlalchemy.orm.decl_api.DeclarativeMeta", {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "_O": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._O", "name": "_O", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_QueryProperty": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.model._QueryProperty", "kind": "Gdef"}, "_Table": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.table._Table", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.extension.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.extension.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.extension.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.extension.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.extension.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.extension.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_app_ctx_id": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.session._app_ctx_id", "kind": "Gdef"}, "_get_2x_declarative_bases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.extension._get_2x_declarative_bases", "name": "_get_2x_declarative_bases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model_class"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSA_MCT", "id": -1, "name": "_FSA_MCT", "namespace": "flask_sqlalchemy.extension._get_2x_declarative_bases", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}, "sqlalchemy.orm.decl_api.DeclarativeMeta", {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_2x_declarative_bases", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.extension._FSA_MCT", "id": -1, "name": "_FSA_MCT", "namespace": "flask_sqlalchemy.extension._get_2x_declarative_bases", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}, "sqlalchemy.orm.decl_api.DeclarativeMeta", {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBase"}, {".class": "TypeType", "item": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "abort": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.abort", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "has_app_context": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.has_app_context", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sa": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy", "kind": "Gdef"}, "sa_event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "sa_orm": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/extension.py"}