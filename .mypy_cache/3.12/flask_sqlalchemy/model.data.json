{".class": "MypyFile", "_fullname": "flask_sqlalchemy.model", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BindMetaMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model.BindMetaMixin", "name": "BindMetaMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.BindMetaMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model.BindMetaMixin", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__fsa__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.BindMetaMixin.__fsa__", "name": "__fsa__", "type": "flask_sqlalchemy.extension.SQLAlchemy"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "name", "bases", "d", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.BindMetaMixin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "name", "bases", "d", "kwargs"], "arg_types": ["flask_sqlalchemy.model.BindMetaMixin", "builtins.str", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BindMetaMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.BindMetaMixin.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model.BindMetaMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model.BindMetaMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BindMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model.BindMixin", "name": "BindMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.BindMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model.BindMixin", "builtins.object"], "names": {".class": "SymbolTable", "__fsa__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.BindMixin.__fsa__", "name": "__fsa__", "type": "flask_sqlalchemy.extension.SQLAlchemy"}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "flask_sqlalchemy.model.BindMixin.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "flask_sqlalchemy.model.BindMixin"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of BindMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.model.BindMixin.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "flask_sqlalchemy.model.BindMixin"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of BindMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.BindMixin.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model.BindMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model.BindMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flask_sqlalchemy.model.BindMetaMixin", "flask_sqlalchemy.model.NameMetaMixin", "sqlalchemy.orm.decl_api.DeclarativeMeta"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model.DefaultMeta", "name": "DefaultMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.DefaultMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model.DefaultMeta", "flask_sqlalchemy.model.BindMetaMixin", "flask_sqlalchemy.model.NameMetaMixin", "sqlalchemy.orm.decl_api.DeclarativeMeta", "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api._DynamicAttributesType", "builtins.type", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model.DefaultMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model.DefaultMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultMetaNoName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["flask_sqlalchemy.model.BindMetaMixin", "sqlalchemy.orm.decl_api.DeclarativeMeta"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model.DefaultMetaNoName", "name": "DefaultMetaNoName", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.DefaultMetaNoName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model.DefaultMetaNoName", "flask_sqlalchemy.model.BindMetaMixin", "sqlalchemy.orm.decl_api.DeclarativeMeta", "sqlalchemy.orm.decl_api.DeclarativeAttributeIntercept", "sqlalchemy.orm.decl_api._DynamicAttributesType", "builtins.type", "sqlalchemy.inspection.Inspectable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model.DefaultMetaNoName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model.DefaultMetaNoName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model.Model", "name": "Model", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.Model", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model.Model", "builtins.object"], "names": {".class": "SymbolTable", "__fsa__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "flask_sqlalchemy.model.Model.__fsa__", "name": "__fsa__", "type": "flask_sqlalchemy.extension.SQLAlchemy"}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.Model.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["flask_sqlalchemy.model.Model"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Model", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "flask_sqlalchemy.model.Model.query", "name": "query", "type": "flask_sqlalchemy.query.Query"}}, "query_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "flask_sqlalchemy.model.Model.query_class", "name": "query_class", "type": {".class": "TypeType", "item": "flask_sqlalchemy.query.Query"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model.Model.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model.Model", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NameMetaMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model.NameMetaMixin", "name": "NameMetaMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.NameMetaMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model.NameMetaMixin", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "name", "bases", "d", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.NameMetaMixin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "name", "bases", "d", "kwargs"], "arg_types": ["flask_sqlalchemy.model.NameMetaMixin", "builtins.str", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NameMetaMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__table__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.NameMetaMixin.__table__", "name": "__table__", "type": "sqlalchemy.sql.schema.Table"}}, "__table_cls__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.NameMetaMixin.__table_cls__", "name": "__table_cls__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": ["flask_sqlalchemy.model.NameMetaMixin", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__table_cls__ of NameMetaMixin", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.NameMetaMixin.__tablename__", "name": "__tablename__", "type": "builtins.str"}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.NameMetaMixin.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model.NameMetaMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model.NameMetaMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NameMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model.NameMixin", "name": "NameMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.NameMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model.NameMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "flask_sqlalchemy.model.NameMixin.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "flask_sqlalchemy.model.NameMixin"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of NameMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.model.NameMixin.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "flask_sqlalchemy.model.NameMixin"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of NameMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__table__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.NameMixin.__table__", "name": "__table__", "type": "sqlalchemy.sql.schema.Table"}}, "__table_cls__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "flask_sqlalchemy.model.NameMixin.__table_cls__", "name": "__table_cls__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "flask_sqlalchemy.model.NameMixin"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__table_cls__ of NameMixin", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "flask_sqlalchemy.model.NameMixin.__table_cls__", "name": "__table_cls__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "flask_sqlalchemy.model.NameMixin"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__table_cls__ of NameMixin", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.NameMixin.__tablename__", "name": "__tablename__", "type": "builtins.str"}}, "metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "flask_sqlalchemy.model.NameMixin.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model.NameMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model.NameMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Query": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.query.Query", "kind": "Gdef"}, "SQLAlchemy": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.extension.SQLAlchemy", "kind": "Gdef"}, "_QueryProperty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask_sqlalchemy.model._QueryProperty", "name": "_QueryProperty", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model._QueryProperty", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask_sqlalchemy.model", "mro": ["flask_sqlalchemy.model._QueryProperty", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model._QueryProperty.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "arg_types": ["flask_sqlalchemy.model._QueryProperty", {".class": "UnionType", "items": ["flask_sqlalchemy.model.Model", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": "flask_sqlalchemy.model.Model"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of _QueryProperty", "ret_type": "flask_sqlalchemy.query.Query", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask_sqlalchemy.model._QueryProperty.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask_sqlalchemy.model._QueryProperty", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.model.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.model.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.model.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.model.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.model.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask_sqlalchemy.model.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "camel_to_snake_case": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.camel_to_snake_case", "name": "camel_to_snake_case", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "camel_to_snake_case", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sa": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy", "kind": "Gdef"}, "sa_orm": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm", "kind": "Gdef"}, "should_set_tablename": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask_sqlalchemy.model.should_set_tablename", "name": "should_set_tablename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": ["builtins.type"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_set_tablename", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/model.py"}