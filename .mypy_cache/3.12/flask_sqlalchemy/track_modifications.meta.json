{"data_mtime": 1748629307, "dep_lines": [6, 7, 10, 13, 1, 3, 5, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 25, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event", "sqlalchemy.orm", "flask.signals", "flask_sqlalchemy.session", "__future__", "typing", "sqlalchemy", "flask", "builtins", "_frozen_importlib", "_typeshed", "abc", "blinker", "blinker.base", "sqlalchemy.event.registry", "sqlalchemy.orm.scoping", "sqlalchemy.orm.session"], "hash": "f196150bdfd142d56072b8f59b9b15c93533bcb1", "id": "flask_sqlalchemy.track_modifications", "ignore_all": true, "interface_hash": "ad364a0daf6ca2a4ffb40cb82500eb25a6210de0", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/track_modifications.py", "plugin_data": null, "size": 2755, "suppressed": [], "version_id": "1.15.0"}