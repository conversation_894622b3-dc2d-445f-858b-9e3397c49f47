{"data_mtime": 1748629307, "dep_lines": [10, 11, 12, 18, 24, 26, 27, 29, 321, 377, 383, 1, 3, 4, 5, 6, 7, 9, 13, 377, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 5, 10, 10, 10, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.orm", "flask_sqlalchemy.model", "flask_sqlalchemy.pagination", "flask_sqlalchemy.query", "flask_sqlalchemy.session", "flask_sqlalchemy.table", "flask_sqlalchemy.cli", "flask_sqlalchemy.record_queries", "flask_sqlalchemy.track_modifications", "__future__", "os", "types", "typing", "warnings", "weakref", "sqlalchemy", "flask", "flask_sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "_warnings", "abc", "flask.app", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm.scoping", "sqlalchemy.orm.session", "sqlalchemy.sql", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "12584a1d4635883c82817ce1bf445d27ae08b464", "id": "flask_sqlalchemy.extension", "ignore_all": true, "interface_hash": "5b6bb469fae69f48ae21dd52c82f4216122d4a91", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/extension.py", "plugin_data": null, "size": 38261, "suppressed": [], "version_id": "1.15.0"}