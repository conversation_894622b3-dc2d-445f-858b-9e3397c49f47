{"data_mtime": 1748629307, "dep_lines": [5, 1, 3, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 30, 30], "dependencies": ["flask_sqlalchemy.extension", "__future__", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "7484836274aa98e07926bca1205e90918cb23689", "id": "flask_sqlalchemy", "ignore_all": true, "interface_hash": "fa677dc894d3d2edaa989abc3b4d4bf903b55cb2", "mtime": 1748626999, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/venv/lib/python3.12/site-packages/flask_sqlalchemy/__init__.py", "plugin_data": null, "size": 653, "suppressed": [], "version_id": "1.15.0"}