{"data_mtime": 1748639576, "dep_lines": [7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app", "os", "sys", "dotenv", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "dotenv.main", "flask", "flask.app", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "flask_sqlalchemy", "flask_sqlalchemy.extension", "typing"], "hash": "8bd8b3cf21ee6091b5706c17c677692f483d1de4", "id": "run_api", "ignore_all": false, "interface_hash": "4b0d34c6efda8696026093a04d3239ca05448129", "mtime": 1748639570, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/root/GitHub/Quizolia/quizolia/run_api.py", "plugin_data": null, "size": 2105, "suppressed": [], "version_id": "1.15.0"}