{".class": "MypyFile", "_fullname": "app", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Api": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.Api", "name": "Api", "type": {".class": "AnyType", "missing_import_name": "app.Api", "source_any": null, "type_of_any": 3}}}, "CORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.CORS", "name": "CORS", "type": {".class": "AnyType", "missing_import_name": "app.CORS", "source_any": null, "type_of_any": 3}}}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "MultiQuiz": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.MultiQuiz", "name": "MultiQuiz", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.MultiQuiz", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app", "mro": ["app.MultiQuiz", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.MultiQuiz.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.MultiQuiz.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.MultiQuiz.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.MultiQuiz", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}}}, "QuizCategories": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.QuizCategories", "name": "QuizCategories", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.QuizCategories", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app", "mro": ["app.QuizCategories", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.QuizCategories.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.QuizCategories.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.QuizCategories.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.QuizCategories", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RandomQuiz": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.RandomQuiz", "name": "RandomQuiz", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.RandomQuiz", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app", "mro": ["app.RandomQuiz", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.RandomQuiz.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.RandomQuiz.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.RandomQuiz.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.RandomQuiz", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.Resource", "source_any": null, "type_of_any": 3}}}, "SQLAlchemy": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.extension.SQLAlchemy", "kind": "Gdef"}, "UserDashboard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.UserDashboard", "name": "UserDashboard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.UserDashboard", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app", "mro": ["app.UserDashboard", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.UserDashboard.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.UserDashboard.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.UserDashboard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.UserDashboard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.UserProfile", "name": "UserProfile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.UserProfile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app", "mro": ["app.UserProfile", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.UserProfile.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.UserProfile.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.UserProfile.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.UserProfile.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.UserProfile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.UserProfile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserQuizzes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.UserQuizzes", "name": "UserQuizzes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.UserQuizzes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app", "mro": ["app.UserQuizzes", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.UserQuizzes.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.UserQuizzes.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.UserQuizzes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.UserQuizzes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Users": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.Users", "name": "Users", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.Users", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app", "mro": ["app.Users", "builtins.object"], "names": {".class": "SymbolTable", "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.Users.__tablename__", "name": "__tablename__", "type": "builtins.str"}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.created_at", "name": "created_at", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.email", "name": "email", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "firstname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.firstname", "name": "firstname", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "last_login": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.last_login", "name": "last_login", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "lastname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.lastname", "name": "lastname", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.password", "name": "password", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.Users.to_dict", "name": "to_dict", "type": null}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.updated_at", "name": "updated_at", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "userId": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.Users.userId", "name": "userId", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.Users.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.Users", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "api": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api", "name": "api", "type": {".class": "AnyType", "missing_import_name": "app.Api", "source_any": {".class": "AnyType", "missing_import_name": "app.Api", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "api_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api_info", "name": "api_info", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.api_info", "name": "api_info", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "api_info", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.app", "name": "app", "type": "flask.app.Flask"}}, "check_password_hash": {".class": "SymbolTableNode", "cross_ref": "werkzeug.security.check_password_hash", "kind": "Gdef"}, "create_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.create_tables", "name": "create_tables", "type": null}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "db": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.db", "name": "db", "type": "flask_sqlalchemy.extension.SQLAlchemy"}}, "debug": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.debug", "name": "debug", "type": "builtins.bool"}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.fields", "source_any": null, "type_of_any": 3}}}, "generate_access_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.generate_access_token", "name": "generate_access_token", "type": null}}, "generate_password_hash": {".class": "SymbolTableNode", "cross_ref": "werkzeug.security.generate_password_hash", "kind": "Gdef"}, "generate_refresh_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.generate_refresh_token", "name": "generate_refresh_token", "type": null}}, "health_check": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.health_check", "name": "health_check", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.health_check", "name": "health_check", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "health_check", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "jwt": {".class": "SymbolTableNode", "cross_ref": "jwt", "kind": "Gdef"}, "jwt_required": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.jwt_required", "name": "jwt_required", "type": null}}, "leaderboard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.leaderboard", "name": "leaderboard", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.leaderboard", "name": "leaderboard", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "load_dotenv": {".class": "SymbolTableNode", "cross_ref": "dotenv.main.load_dotenv", "kind": "Gdef"}, "login": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.login", "name": "login", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.login", "name": "login", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "login", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "logout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.logout", "name": "logout", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.logout", "name": "logout", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logout", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "make_response": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.make_response", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "port": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.port", "name": "port", "type": "builtins.int"}}, "profile_update_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.profile_update_model", "name": "profile_update_model", "type": {".class": "AnyType", "missing_import_name": "app.Api", "source_any": {".class": "AnyType", "missing_import_name": "app.Api", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "quiz_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.quiz_ns", "name": "quiz_ns", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "signup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.signup", "name": "signup", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.signup", "name": "signup", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "user_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.user_model", "name": "user_model", "type": {".class": "AnyType", "missing_import_name": "app.Api", "source_any": {".class": "AnyType", "missing_import_name": "app.Api", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "user_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.user_ns", "name": "user_ns", "type": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "verify_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["token", "token_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.verify_token", "name": "verify_token", "type": null}}, "verify_token_endpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.verify_token_endpoint", "name": "verify_token_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.verify_token_endpoint", "name": "verify_token_endpoint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "/root/GitHub/Quizolia/quizolia/app.py"}