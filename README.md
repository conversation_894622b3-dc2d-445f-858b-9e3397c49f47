# Quizolia - Quiz Application API

## 🎯 Project Overview

Quizolia is a modern quiz application with a **complete API-based backend** featuring JWT cookie authentication, Swagger documentation, and a clean RESTful design. The project has been fully restructured from a template-based Flask application to a comprehensive API backend ready for frontend integration.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- MySQL database
- Virtual environment (already created)

### Installation & Setup

1. **Activate the virtual environment**:
   ```bash
   source venv/bin/activate
   ```

2. **Install dependencies** (already installed):
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment variables** (already configured):
   - `.env` file contains database URI and secret key

4. **Start the API server**:
   ```bash
   python app.py
   ```

5. **Access the application**:
   - **API Base**: http://localhost:5000
   - **Swagger Documentation**: http://localhost:5000/swagger/
   - **Health Check**: http://localhost:5000/health

## 📁 Project Structure

```
quizolia/
├── 📄 app.py                 # Main Flask API application
├── 📄 models.py              # Database models
├── 📄 config.py              # Configuration settings
├── 📄 run_api.py             # Alternative startup script
├── 📄 test_api.py            # Comprehensive test suite
├── 📄 requirements.txt       # Python dependencies
├── 📄 .env                   # Environment variables
├── 📄 README.md              # This file
├── 📄 API_README.md          # Detailed API documentation
├── 📁 venv/                  # Virtual environment
└── 📁 quizolia_old/          # Legacy template-based files
    ├── app.py                # Original Flask app
    ├── templates/            # HTML templates
    ├── static/               # CSS, JS, images
    └── ...                   # Other legacy files
```

## 🔐 Authentication System

### JWT Cookie-Based Authentication
- **Access Token**: 15-minute lifespan, stored in HTTP-only cookie
- **Refresh Token**: 30-day lifespan, stored in HTTP-only cookie
- **Security Features**: XSS protection, CSRF protection, secure signing

### Authentication Flow
1. User registers/logs in → Receives JWT tokens in secure cookies
2. Frontend makes API calls → Cookies sent automatically
3. Backend verifies tokens → Grants/denies access
4. Automatic token refresh → Seamless user experience

## 📚 API Endpoints

### Authentication
- `POST /api/auth/signup` - User registration
- `POST /api/auth/login` - User login (sets JWT cookies)
- `POST /api/auth/logout` - User logout (clears cookies)
- `GET /api/auth/verify` - Token verification

### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `GET /api/user/dashboard` - Get dashboard data
- `GET /api/user/quizzes` - Get user's quiz history

### Quiz System
- `GET /api/quiz/categories` - Get quiz categories
- `GET /api/quiz/random` - Get random quiz
- `GET /api/quiz/multi` - Get multi-category quiz

### Leaderboard
- `GET /api/leaderboard` - Get global leaderboard

### Utility
- `GET /health` - Health check
- `GET /api` - API information
- `GET /swagger/` - Interactive API documentation

## 🧪 Testing

### Run the test suite:
```bash
python test_api.py
```

### Manual testing examples:

**User Registration:**
```bash
curl -X POST http://localhost:5000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "firstname": "John",
    "lastname": "Doe",
    "email": "<EMAIL>",
    "password": "securepassword"
  }'
```

**User Login:**
```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -c cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword"
  }'
```

**Access Protected Endpoint:**
```bash
curl -X GET http://localhost:5000/api/user/profile \
  -b cookies.txt
```

## 🌐 Frontend Integration

### Example JavaScript Usage:
```javascript
// Login
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ 
    email: '<EMAIL>', 
    password: 'password' 
  }),
  credentials: 'include' // Important for cookies
});

// Get user profile (authenticated)
const profile = await fetch('/api/user/profile', {
  credentials: 'include' // Cookies sent automatically
});
```

## 📊 API Response Format

### Success Response:
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data here
  }
}
```

### Error Response:
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

## 🛡️ Security Features

- **HTTP-Only Cookies** - XSS protection
- **Secure Password Hashing** - Werkzeug implementation
- **JWT Token Signing** - Secret key based
- **CORS Configuration** - Controlled cross-origin access
- **Token Expiration** - Automatic lifecycle management

## 🔧 Development

### Key Technologies:
- **Flask** - Web framework
- **Flask-RESTX** - API framework with Swagger
- **SQLAlchemy** - Database ORM
- **PyJWT** - JWT token handling
- **Flask-CORS** - Cross-origin support
- **Werkzeug** - Password hashing

### Database Models:
- **Users** - User authentication and profile data
- *Future models for quizzes, questions, results*

## 📈 What's Next

The API is now ready for:
1. **Frontend Development** - React, Vue, Angular, or any SPA framework
2. **Mobile App Integration** - React Native, Flutter, etc.
3. **Quiz System Expansion** - Add more quiz models and functionality
4. **Real-time Features** - WebSocket integration for live quizzes
5. **Analytics** - User performance tracking and insights

## 🎉 Migration Complete

✅ **Template-based Flask app** → **API-based backend**  
✅ **Session authentication** → **JWT cookie authentication**  
✅ **No documentation** → **Swagger documentation**  
✅ **Monolithic structure** → **Clean, modular architecture**  
✅ **Frontend coupled** → **Frontend agnostic**  

The old template-based files are preserved in the `quizolia_old/` directory for reference.

## 📞 Support

For detailed API documentation, visit: http://localhost:5000/swagger/

The API is fully functional and ready for frontend integration!

---

Built with 💖 by [Daniel Dohou](https://github.com/dohoudaniel) and [Precious Ese](https://github.com/Layna934)
