:root {
    /* Light theme */
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --primary-foreground: #ffffff;
    --background: #ffffff;
    --surface: #f8fafc;
    --surface-hover: #f1f5f9;
    --text: #0f172a;
    --text-secondary: #475569;
    --border: #e2e8f0;
    --card-background: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Dark theme */
.dark-theme {
    --primary-color: #818cf8;
    --primary-hover: #6366f1;
    --primary-foreground: #ffffff;
    --background: #0f172a;
    --surface: #1e293b;
    --surface-hover: #334155;
    --text: #f8fafc;
    --text-secondary: #94a3b8;
    --border: #334155;
    --card-background: #1e293b;
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background);
    color: var(--text);
    line-height: 1.5;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header styles */
.header {
    background-color: var(--surface);
    border-bottom: 1px solid var(--border);
    padding: 0.75rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(8px);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--text);
    font-weight: 600;
    font-size: 1.25rem;
    transition: color 0.2s ease;
}

.logo:hover {
    color: var(--primary-color);
}

.logo-icon {
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.logo:hover .logo-icon {
    transform: scale(1.1);
}

/* Navigation styles */
.nav-desktop {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--text);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.nav-link:hover::after {
    transform: scaleX(1);
}

/* Header actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.score-display {
    background-color: var(--primary-color);
    color: var(--primary-foreground);
    padding: 0.5rem 1.25rem;
    border-radius: 9999px;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.theme-toggle {
    background: var(--surface);
    border: 1px solid var(--border);
    color: var(--text);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background-color: var(--surface-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.theme-toggle svg {
    transition: transform 0.5s ease;
}

.theme-toggle:hover svg {
    transform: rotate(360deg);
}

.dark-theme .sun-icon {
    display: none;
}

.light-theme .moon-icon {
    display: none;
}

/* Main content */
.main-content {
    padding: 3rem 0;
    min-height: calc(100vh - 140px);
}

.page-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Quiz options */
.quiz-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 1rem;
}

.quiz-card {
    background-color: var(--card-background);
    border-radius: 1rem;
    overflow: hidden;
    text-decoration: none;
    color: var(--text);
    transition: all 0.3s ease;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-md);
}

.quiz-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.quiz-card[data-type="random"]:hover {
    border-color: var(--primary-color);
}

.quiz-card[data-type="categorized"]:hover {
    border-color: var(--primary-hover);
}

.quiz-card-image {
    position: relative;
    overflow: hidden;
}

.quiz-card-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.quiz-card:hover .quiz-card-image img {
    transform: scale(1.1);
}

.quiz-card-content {
    padding: 1.5rem;
}

.quiz-card-content h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text);
}

.quiz-card-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer */
.footer {
    background-color: var(--surface);
    border-top: 1px solid var(--border);
    padding: 1.5rem 0;
    text-align: center;
    color: var(--text-secondary);
}

/* Responsive design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-desktop {
        gap: 1rem;
    }

    .page-title {
        font-size: 2rem;
        padding: 0 1rem;
    }

    .quiz-options {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.quiz-card {
    animation: fadeIn 0.6s ease-out;
    animation-fill-mode: both;
}

.quiz-card:nth-child(2) {
    animation-delay: 0.2s;
}
