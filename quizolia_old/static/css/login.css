/* Header styles */
header {
    background-color: var(--background-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: background-color 0.3s ease;
}

body.dark-mode header {
    background-color: var(--background-color);
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo::after {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

nav ul li {
    margin-left: 2rem;
}

nav ul li a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: var(--primary-color);
}

nav ul li a.cta-button {
    background-color: var(--primary-color);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

nav ul li a.cta-button:hover {
    background-color: var(--secondary-color);
}

/* Responsive styles for the header */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        align-items: flex-start;
    }

    nav ul {
        margin-top: 1rem;
        flex-direction: column;
        width: 100%;
    }

    nav ul li {
        margin-left: 0;
        margin-bottom: 0.5rem;
    }

    nav ul li:last-child {
        margin-bottom: 0;
    }

    nav ul li a.cta-button {
        display: inline-block;
        width: 100%;
        text-align: center;
    }
}

/* Adjust main content to account for fixed header */
.container {
    margin-top: 80px; /* Adjust this value based on your header height */
}

:root {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --accent-color: #e74c3c;
    --background-color: #f5f5f5;
    --text-color: #333;
    --light-text-color: #777;
    --border-color: #ddd;
    --input-background: #f8f9fa;
    --input-hover-background: #fff;
    --input-focus-background: #fff;
}

body.dark-mode {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --accent-color: #e74c3c;
    --background-color: #1a1a1a;
    --text-color: #f5f5f5;
    --light-text-color: #aaa;
    --border-color: #444;
    --input-background: #2c2c2c;
    --input-hover-background: #333;
    --input-focus-background: #333;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: background-color 0.3s, color 0.3s;
}

.container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.split-screen {
    display: flex;
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.split-screen:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.left {
    flex: 1;
    background: linear-gradient(135deg, 
        var(--primary-color), 
        var(--secondary-color),
        #2980b9,
        #3498db
    );
    background-size: 300% 300%;
    animation: gradientShift 15s ease infinite;
    color: #fff;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.left .content {
    max-width: 400px;
}

.left h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
}

.left p {
    font-size: 1.2em;
    margin-bottom: 30px;
}

.illustration {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
}

.right {
    flex: 1;
    padding: 40px;
    background-color: #fff;
}

.login-container {
    max-width: 400px;
    margin: 0 auto;
}

.login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.login-header h2 {
    font-size: 2em;
    color: var(--text-color);
}

.dark-mode-toggle {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
}

.dark-mode-toggle svg {
    width: 24px;
    height: 24px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
}

input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1em;
    transition: border-color 0.3s;
}

input[type="email"]:focus,
input[type="password"]:focus {
    border: 2px solid var(--primary-color);
    outline: none;
    background-color: var(--input-focus-background, #fff);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--light-text-color);
}

.checkbox {
    display: flex;
    align-items: center;
}

.checkbox input {
    margin-right: 10px;
}

.login-button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background-image: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.login-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.login-button:active {
    transform: translateY(1px);
}

.forgot-password {
    text-align: center;
    margin-top: 20px;
}

.forgot-password a {
    color: var(--primary-color);
    text-decoration: none;
}

.signup-prompt {
    text-align: center;
    margin-top: 30px;
    color: var(--light-text-color);
}

.signup-prompt a {
    color: var(--primary-color);
    text-decoration: none;
}

.language-selector {
    margin-top: 20px;
    text-align: center;
}

.language-selector select {
    padding: 5px 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--background-color);
    color: var(--text-color);
}

footer {
    text-align: center;
    padding: 20px;
    background-color: var(--background-color);
    color: var(--light-text-color);
    position: relative; /* Ensure it stays relative to the content */
    margin-top: 2rem; /* Add spacing between the form and footer */
    clear: both; /* Clears any floating elements above it */
}


footer a {
    color: var(--primary-color);
    text-decoration: none;
}

@media (max-width: 768px) {
    .split-screen {
        flex-direction: column;
    }

    .left, .right {
        width: 100%;
    }

    .left {
        padding: 40px 20px;
    }

    .right {
        padding: 40px 20px;
    }
}

/* Accessibility Styles */
:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Dark mode styles */
body.dark-mode {
    background-color: var(--background-color);
}

body.dark-mode .split-screen {
    background-color: #2c2c2c;
}

body.dark-mode .right {
    background-color: #2c2c2c;
}

body.dark-mode input[type="email"],
body.dark-mode input[type="password"] {
    background-color: #3a3a3a;
    color: var(--text-color);
    border-color: var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.split-screen {
    animation: fadeIn 0.5s ease-out;
}


/* New styles for the provided form structure */
.form__container {
    max-width: 400px;
    margin: 0 auto;
}

.form__labelinput {
    margin-bottom: 20px;
    position: relative; /* Added for error message positioning */
}

.form__labelinput label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
    font-weight: 500;
}

.form__labelinput input {
    width: 100%;
    padding: 10px;
    border: 2px solid transparent;
    border-radius: 5px;
    font-size: 1em;
    transition: all 0.3s ease;
    background-color: var(--input-background, #f8f9fa);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.form__labelinput input:hover {
    background-color: var(--input-hover-background, #fff);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
}

.form__labelinput input:focus {
    background-color: var(--input-focus-background, #fff);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.form__labelinput input[type="email"].valid {
    border-color: #2ecc71;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232ecc71' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
    padding-right: 40px;
}

.form__labelinput input[type="email"].invalid {
    border-color: var(--accent-color);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23e74c3c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
    padding-right: 40px;
}


.error-message {
    color: var(--accent-color);
    font-size: 0.85rem;
    margin-top: 0.3rem;
    opacity: 0;
    height: 0;
    transition: all 0.3s ease;
}

.error-message.visible {
    opacity: 1;
    height: auto;
    margin-bottom: 0.5rem;
}

.form__submit {
    margin-top: 30px;
}

.form__submit button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background-image: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.form__submit button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.form__submit button:active {
    transform: translateY(1px);
}

.form__submit p {
    margin-top: 20px;
    text-align: center;
    color: var(--light-text-color);
}

.form__submit a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.form__submit a:hover {
    text-decoration: underline;
}

/* Responsive styles for the new form structure */
@media (max-width: 768px) {
    .form__container {
        padding: 0 20px;
    }
}

/* Existing styles continue below */