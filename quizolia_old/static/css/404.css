:root {
    /* Light theme */
    --background: #ffffff;
    --foreground: #0f172a;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --muted: #f1f5f9;
    --muted-foreground: #64748b;
    --border: #e2e8f0;
}

.dark {
    /* Dark theme */
    --background: #0f172a;
    --foreground: #f8fafc;
    --primary: #60a5fa;
    --primary-foreground: #0f172a;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --border: #1e293b;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: background-color 0.3s, color 0.3s;
}

.header {
    padding: 1rem;
    border-bottom: 1px solid var(--border);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--foreground);
    font-weight: 700;
}

.logo-icon {
    color: var(--primary);
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--foreground);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
}

.theme-toggle:hover {
    background-color: var(--muted);
}

.dark .sun-icon {
    display: block;
}

.dark .moon-icon {
    display: none;
}

.sun-icon {
    display: none;
}

.moon-icon {
    display: block;
}

.main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.error-container {
    text-align: center;
    max-width: 28rem;
    margin: 0 auto;
}

.error-code {
    font-size: 8rem;
    font-weight: 700;
    line-height: 1;
    color: var(--primary);
    margin-bottom: 1rem;
    animation: floating 3s ease-in-out infinite;
}

.error-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.error-message {
    color: var(--muted-foreground);
    margin-bottom: 2rem;
}

.home-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary);
    color: var(--primary-foreground);
    text-decoration: none;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: transform 0.2s;
}

.home-button:hover {
    transform: translateY(-2px);
}

@keyframes floating {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

.error-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    animation: floating 3s ease-in-out infinite;
}

.error-icon svg {
    width: 100%;
    height: 100%;
    color: var(--primary);
}

@media (max-width: 640px) {
    .error-code {
        font-size: 6rem;
    }

    .error-title {
        font-size: 1.5rem;
    }

    .error-icon {
        width: 80px;
        height: 80px;
    }
}