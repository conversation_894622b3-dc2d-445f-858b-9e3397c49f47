<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuizMaster - Random Quiz</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="light-theme">
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <a href="/" class="logo">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="logo-icon">
                        <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313-12.454z"></path>
                        <path d="M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2"></path>
                        <path d="M19 11h2m-1 -1v2"></path>
                    </svg>
                    <span>QuizMaster</span>
                </a>

                <nav class="nav-desktop">
                    <a href="/my-quizzes" class="nav-link">My Quizzes</a>
                    <a href="/leaderboard" class="nav-link">Leaderboard</a>
                </nav>

                <div class="header-actions">
                    <div class="score-display" id="score-display">
                        Score: <span id="score">0</span>/<span id="total-questions">10</span>
                    </div>
                    <button class="theme-toggle" aria-label="Toggle theme" id="theme-toggle">
                        <svg class="sun-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"></circle>
                            <line x1="12" y1="1" x2="12" y2="3"></line>
                            <line x1="12" y1="21" x2="12" y2="23"></line>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                            <line x1="1" y1="12" x2="3" y2="12"></line>
                            <line x1="21" y1="12" x2="23" y2="12"></line>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                        </svg>
                        <svg class="moon-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="container">
                <!-- Loading State -->
                <div id="loading" class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading your quiz questions...</p>
                </div>

                <!-- Quiz Container -->
                <div id="quiz-container" class="quiz-section hidden">
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div id="progress" class="progress"></div>
                        </div>
                        <div class="progress-text">
                            Question <span id="current-question">1</span>/10
                        </div>
                    </div>

                    <div class="question-card">
                        <h2 id="question" class="question"></h2>
                        <div id="answers" class="answers-grid"></div>
                    </div>

                    <button id="next-btn" class="primary-button hidden">Next Question</button>
                </div>

                <!-- Results Section -->
                <div id="results" class="quiz-section hidden">
                    <div class="results-content">
                        <div class="score-circle">
                            <span id="final-score">0</span>/10
                        </div>
                        <p id="score-message" class="score-message"></p>
                        <button id="restart-btn" class="primary-button">Try Another Quiz</button>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="container">
                <p class="copyright">&copy; 2025 QuizMaster. All rights reserved.</p>
                <nav class="footer-nav">
                    <a href="/terms" class="footer-link">Terms</a>
                    <a href="/privacy" class="footer-link">Privacy</a>
                </nav>
            </div>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>