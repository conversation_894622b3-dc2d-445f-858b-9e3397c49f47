body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f7f7f7;
    color: #333;
  }
  
  header {
    background-color: #4caf50;
    color: white;
    text-align: center;
    padding: 1rem 0;
  }
  
  .quiz-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .settings {
    text-align: center;
  }
  
  label {
    display: block;
    margin: 1rem 0 0.5rem;
    font-weight: bold;
  }
  
  .dropdown {
    padding: 0.5rem;
    font-size: 1rem;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 300px;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin: 1rem 0;
  }
  
  .btn:hover {
    background-color: #45a049;
  }
  
  #quiz-content {
    text-align: center;
  }
  
  #question-text {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }
  
  #options-list {
    list-style-type: none;
    padding: 0;
  }
  
  #options-list li {
    background-color: #f7f7f7;
    border: 1px solid #ccc;
    padding: 0.5rem 1rem;
    margin: 0.5rem 0;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease;
  }
  
  #options-list li:hover {
    background-color: #d9fdd3;
  }
  
  .correct {
    background-color: #4caf50 !important;
    color: white;
  }
  
  .incorrect {
    background-color: #f44336 !important;
    color: white;
  }
  
  .hidden {
    display: none;
  }
  
  footer {
    text-align: center;
    background-color: #4caf50;
    color: white;
    padding: 1rem 0;
  }
  