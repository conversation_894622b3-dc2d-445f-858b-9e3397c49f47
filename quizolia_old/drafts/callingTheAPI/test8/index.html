<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Trivia Quiz</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app">
        <header>
            <h1>Dynamic Trivia Quiz</h1>
        </header>

        <main>
            <div id="quiz-setup" class="quiz-setup">
                <div class="setup-card">
                    <h2>Choose Your Category</h2>
                    <div class="form-group">
                        <label for="category">Select Category:</label>
                        <select id="category" class="select-input">
                            <option value="">Loading categories...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="difficulty">Select Difficulty:</label>
                        <select id="difficulty" class="select-input">
                            <option value="">Any Difficulty</option>
                            <option value="easy">Easy</option>
                            <option value="medium">Medium</option>
                            <option value="hard">Hard</option>
                        </select>
                    </div>
                    <button id="start-quiz" class="primary-button" disabled>Start Quiz</button>
                </div>
            </div>

            <div id="quiz-container" class="quiz-container hidden">
                <div class="progress-bar">
                    <div id="progress" class="progress"></div>
                </div>

                <div class="question-card">
                    <div class="question-header">
                        <span class="category-badge" id="current-category"></span>
                        <span class="question-number">Question <span id="current-question">1</span>/10</span>
                    </div>
                    
                    <h2 id="question" class="question-text"></h2>
                    <div id="answers" class="answers-grid"></div>
                </div>

                <button id="next-btn" class="primary-button hidden">Next Question</button>
            </div>

            <div id="results" class="results hidden">
                <div class="results-card">
                    <h2>Quiz Complete!</h2>
                    <div class="score-display">
                        <div class="score-circle">
                            <span id="final-score">0</span>/10
                        </div>
                    </div>
                    <p class="score-message" id="score-message"></p>
                    <button id="restart-btn" class="primary-button">Try Another Category</button>
                </div>
            </div>

            <div id="error-container" class="error-container hidden">
                <div class="error-card">
                    <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="12" y1="8" x2="12" y2="12"/>
                        <line x1="12" y1="16" x2="12.01" y2="16"/>
                    </svg>
                    <h2>Oops!</h2>
                    <p id="error-message">Something went wrong. Please try again.</p>
                    <button onclick="location.reload()" class="primary-button">Reload Page</button>
                </div>
            </div>
        </main>

        <footer>
            <p>Powered by Open Trivia Database</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>