:root {
    /* Light theme */
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --background: #ffffff;
    --surface: #f3f4f6;
    --text: #1f2937;
    --text-secondary: #6b7280;
    --border: #e5e7eb;
    --correct: #059669;
    --incorrect: #dc2626;
}

/* Dark theme */
.dark-theme {
    --background: #1f2937;
    --surface: #374151;
    --text: #f3f4f6;
    --text-secondary: #9ca3af;
    --border: #4b5563;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: system-ui, -apple-system, sans-serif;
    background-color: var(--background);
    color: var(--text);
    line-height: 1.5;
    transition: background-color 0.3s, color 0.3s;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header styles */
.header {
    background-color: var(--surface);
    border-bottom: 1px solid var(--border);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--text);
    font-weight: 600;
    font-size: 1.25rem;
}

.logo-icon {
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.score-display {
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-weight: 600;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.3s;
}

.theme-toggle:hover {
    background-color: var(--border);
}

.dark-theme .sun-icon {
    display: none;
}

.light-theme .moon-icon {
    display: none;
}

/* Main content styles */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 140px);
}

.quiz-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background-color: var(--surface);
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.setup-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

select {
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border);
    background-color: var(--background);
    color: var(--text);
    font-size: 1rem;
    cursor: pointer;
}

.primary-button {
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
}

.primary-button:hover:not(:disabled) {
    background-color: var(--primary-hover);
}

.primary-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Progress bar */
.progress-container {
    margin-bottom: 2rem;
}

.progress-bar {
    height: 0.5rem;
    background-color: var(--border);
    border-radius: 9999px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: var(--primary-color);
    width: 0;
    transition: width 0.3s ease;
}

.progress-text {
    margin-top: 0.5rem;
    text-align: right;
    color: var(--text-secondary);
}

/* Question styles */
.category-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: 9999px;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.question {
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

.answers-grid {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.answer-button {
    padding: 1rem;
    background-color: var(--background);
    border: 1px solid var(--border);
    border-radius: 0.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.answer-button:hover:not(:disabled) {
    background-color: var(--border);
}

.answer-button.correct {
    background-color: var(--correct);
    color: white;
    border-color: var(--correct);
}

.answer-button.incorrect {
    background-color: var(--incorrect);
    color: white;
    border-color: var(--incorrect);
}

/* Results styles */
.results-content {
    text-align: center;
}

.score-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 600;
    margin: 2rem auto;
}

.score-message {
    font-size: 1.25rem;
    margin-bottom: 2rem;
}

/* Error styles */
.error-content {
    text-align: center;
}

.error-icon {
    color: var(--incorrect);
    margin-bottom: 1rem;
}

/* Footer styles */
.footer {
    background-color: var(--surface);
    border-top: 1px solid var(--border);
    padding: 1rem 0;
    text-align: center;
    color: var(--text-secondary);
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .quiz-section {
        padding: 1.5rem;
        margin: 0 1rem;
    }

    .question {
        font-size: 1.25rem;
    }

    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .score-display {
        font-size: 0.875rem;
    }
}

@media (min-width: 640px) {
    .answers-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.quiz-section:not(.hidden) {
    animation: fadeIn 0.3s ease-out;
}