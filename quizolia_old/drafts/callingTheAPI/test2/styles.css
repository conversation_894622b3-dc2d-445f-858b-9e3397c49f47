:root {
    /* Light theme */
    --background: #ffffff;
    --foreground: #0f172a;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --muted: #f1f5f9;
    --muted-foreground: #64748b;
    --border: #e2e8f0;
    --radius: 0.5rem;
}

.dark {
    /* Dark theme */
    --background: #0f172a;
    --foreground: #f8fafc;
    --primary: #60a5fa;
    --primary-foreground: #0f172a;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --border: #1e293b;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    line-height: 1.5;
    transition: background-color 0.3s, color 0.3s;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header styles */
.header {
    border-bottom: 1px solid var(--border);
    background-color: var(--background);
}

.header .container {
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--foreground);
    font-weight: 700;
    font-size: 1.25rem;
}

.logo-icon {
    color: var(--primary);
}

.nav-desktop {
    display: none;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle, .profile-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 9999px;
    border: none;
    background: transparent;
    color: var(--foreground);
    cursor: pointer;
    transition: background-color 0.2s;
}

.theme-toggle:hover, .profile-link:hover {
    background-color: var(--muted);
}

.theme-toggle:focus-visible, .profile-link:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.dark .sun-icon {
    display: block;
}

.dark .moon-icon {
    display: none;
}

.sun-icon {
    display: none;
}

.moon-icon {
    display: block;
}

/* Mobile navigation */
.nav-mobile {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border);
    background-color: var(--background);
}

.nav-link {
    color: var(--foreground);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s;
}

.nav-link:hover {
    color: var(--primary);
}

/* Main content */
.main {
    flex: 1;
    padding: 2rem 0;
}

.main-content {
    display: grid;
    gap: 2rem;
    align-items: center;
    text-align: center;
}

.content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    animation: fadeInUp 0.5s ease-out;
}

.subtitle {
    color: var(--muted-foreground);
    animation: fadeInUp 0.5s ease-out 0.1s backwards;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    background-color: var(--primary);
    color: var(--primary-foreground);
    font-weight: 600;
    text-decoration: none;
    border-radius: var(--radius);
    transition: transform 0.2s;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.5s ease-out 0.2s backwards;
}

.cta-button:hover {
    transform: translateY(-2px);
}

.button-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        120deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shine 3s infinite;
}

.illustration {
    animation: fadeInUp 0.5s ease-out 0.3s backwards;
}

.illustration img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Footer */
.footer {
    border-top: 1px solid var(--border);
    background-color: var(--background);
}

.footer .container {
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.copyright {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

.footer-nav {
    display: flex;
    gap: 1rem;
}

.footer-link {
    color: var(--muted-foreground);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s;
}

.footer-link:hover {
    color: var(--primary);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shine {
    to {
        left: 100%;
    }
}

/* Responsive design */
@media (min-width: 768px) {
    .nav-desktop {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .nav-mobile {
        display: none;
    }

    .main {
        padding: 4rem 0;
    }

    .main-content {
        grid-template-columns: 1fr 1fr;
        text-align: left;
    }

    .content-wrapper {
        align-items: flex-start;
    }

    .title {
        font-size: 3.5rem;
    }
}

/* Accessibility focus styles */
:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* App wrapper */
.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}