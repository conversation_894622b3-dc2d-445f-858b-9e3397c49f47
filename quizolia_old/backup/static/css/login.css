/* Header styles */
header {
    background-color: var(--background-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: background-color 0.3s ease;
}

body.dark-mode header {
    background-color: var(--background-color);
    box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo::after {
    margin-left: 0.5rem;
    font-size: 1.2rem;
}

nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

nav ul li {
    margin-left: 2rem;
}

nav ul li a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: var(--primary-color);
}

nav ul li a.cta-button {
    background-color: var(--primary-color);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

nav ul li a.cta-button:hover {
    background-color: var(--secondary-color);
}

/* Responsive styles for the header */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
        align-items: flex-start;
    }

    nav ul {
        margin-top: 1rem;
        flex-direction: column;
        width: 100%;
    }

    nav ul li {
        margin-left: 0;
        margin-bottom: 0.5rem;
    }

    nav ul li:last-child {
        margin-bottom: 0;
    }

    nav ul li a.cta-button {
        display: inline-block;
        width: 100%;
        text-align: center;
    }
}

/* Adjust main content to account for fixed header */
.container {
    margin-top: 80px; /* Adjust this value based on your header height */
}

:root {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --accent-color: #e74c3c;
    --background-color: #f5f5f5;
    --text-color: #333;
    --light-text-color: #777;
    --border-color: #ddd;
}

body.dark-mode {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --accent-color: #e74c3c;
    --background-color: #1a1a1a;
    --text-color: #f5f5f5;
    --light-text-color: #aaa;
    --border-color: #444;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: background-color 0.3s, color 0.3s;
}

.container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.split-screen {
    display: flex;
    width: 100%;
    max-width: 1200px;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.left {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: #fff;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.left .content {
    max-width: 400px;
}

.left h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
}

.left p {
    font-size: 1.2em;
    margin-bottom: 30px;
}

.illustration {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
}

.right {
    flex: 1;
    padding: 40px;
    background-color: #fff;
}

.login-container {
    max-width: 400px;
    margin: 0 auto;
}

.login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.login-header h2 {
    font-size: 2em;
    color: var(--text-color);
}

.dark-mode-toggle {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
}

.dark-mode-toggle svg {
    width: 24px;
    height: 24px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
}

input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1em;
    transition: border-color 0.3s;
}

input[type="email"]:focus,
input[type="password"]:focus {
    border-color: var(--primary-color);
    outline: none;
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--light-text-color);
}

.checkbox {
    display: flex;
    align-items: center;
}

.checkbox input {
    margin-right: 10px;
}

.login-button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s;
}

.login-button:hover {
    background-color: var(--secondary-color);
}

.forgot-password {
    text-align: center;
    margin-top: 20px;
}

.forgot-password a {
    color: var(--primary-color);
    text-decoration: none;
}

.signup-prompt {
    text-align: center;
    margin-top: 30px;
    color: var(--light-text-color);
}

.signup-prompt a {
    color: var(--primary-color);
    text-decoration: none;
}

.language-selector {
    margin-top: 20px;
    text-align: center;
}

.language-selector select {
    padding: 5px 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--background-color);
    color: var(--text-color);
}

/* footer {
    text-align: center;
    padding: 20px;
    background-color: var(--background-color);
    color: var(--light-text-color);
} */
footer {
    text-align: center;
    padding: 20px;
    background-color: var(--background-color);
    color: var(--light-text-color);
    position: relative; /* Ensure it stays relative to the content */
    margin-top: 2rem; /* Add spacing between the form and footer */
    clear: both; /* Clears any floating elements above it */
}


footer a {
    color: var(--primary-color);
    text-decoration: none;
}

@media (max-width: 768px) {
    .split-screen {
        flex-direction: column;
    }

    .left, .right {
        width: 100%;
    }

    .left {
        padding: 40px 20px;
    }

    .right {
        padding: 40px 20px;
    }
}

/* Accessibility Styles */
:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Dark mode styles */
body.dark-mode {
    background-color: var(--background-color);
}

body.dark-mode .split-screen {
    background-color: #2c2c2c;
}

body.dark-mode .right {
    background-color: #2c2c2c;
}

body.dark-mode input[type="email"],
body.dark-mode input[type="password"] {
    background-color: #3a3a3a;
    color: var(--text-color);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.split-screen {
    animation: fadeIn 0.5s ease-out;
}

.login-button {
    transition: transform 0.1s;
}

.login-button:active {
    transform: scale(0.98);
}


/* New styles for the provided form structure */
.form__container {
    max-width: 400px;
    margin: 0 auto;
}

.form__labelinput {
    margin-bottom: 20px;
}

.form__labelinput label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
    font-weight: 500;
}

.form__labelinput input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1em;
    transition: border-color 0.3s;
}

.form__labelinput input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form__submit {
    margin-top: 30px;
}

.form__submit button {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.3s;
}

.form__submit button:hover {
    background-color: var(--secondary-color);
}

.form__submit p {
    margin-top: 20px;
    text-align: center;
    color: var(--light-text-color);
}

.form__submit a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.form__submit a:hover {
    text-decoration: underline;
}

/* Dark mode styles for the new form structure */
body.dark-mode .form__labelinput input {
    background-color: #3a3a3a;
    color: var(--text-color);
    border-color: var(--border-color);
}

body.dark-mode .form__submit button {
    background-color: var(--primary-color);
}

body.dark-mode .form__submit button:hover {
    background-color: var(--secondary-color);
}

/* Responsive styles for the new form structure */
@media (max-width: 768px) {
    .form__container {
        padding: 0 20px;
    }
}

/* Existing styles continue below */
