:root {
    --primary-color: #6200ea;
    --secondary-color: #03dac6;
    --background-color: #f5f5f5;
    --text-color: #333;
    --white: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

header {
    background-color: var(--white);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 2rem;
}

nav ul li a {
    text-decoration: none;
    color: var(--text-color);
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: var(--primary-color);
}

.cta-button {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.cta-button:hover {
    background-color: var(--secondary-color);
}

main {
    padding-top: 80px;
}

section {
    padding: 4rem 5%;
}

#hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    text-align: center;
    padding: 6rem 5%;
}

#hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

#hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

#features {
    background-color: var(--white);
}

#features h2 {
    text-align: center;
    margin-bottom: 2rem;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background-color: var(--background-color);
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

#testimonials {
    background-color: var(--white);
    text-align: center;
}

#testimonials h2 {
    margin-bottom: 2rem;
}

.testimonial-slider {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
}

.testimonial {
    flex: 0 0 100%;
    scroll-snap-align: start;
    padding: 2rem;
}

.testimonial img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.testimonial blockquote {
    font-style: italic;
    margin-bottom: 1rem;
}

#cta {
    background-color: var(--secondary-color);
    color: var(--white);
    text-align: center;
}

#cta h2 {
    margin-bottom: 2rem;
}

footer {
    background-color: var(--primary-color);
    color: var(--white);
    text-align: center;
    padding: 2rem 5%;
}

.footer-links {
    margin-bottom: 1rem;
}

.footer-links a {
    color: var(--white);
    text-decoration: none;
    margin: 0 1rem;
}

.social-icons {
    margin-bottom: 1rem;
}

.social-icons a {
    color: var(--white);
    font-size: 1.5rem;
    margin: 0 0.5rem;
}

@media (max-width: 768px) {
    nav {
        flex-direction: column;
    }

    nav ul {
        margin-top: 1rem;
    }

    nav ul li {
        margin-left: 0;
        margin-right: 1rem;
    }
}

:root {
    /* Light theme variables */
    --background: #ffffff;
    --foreground: #0f172a;
    --primary: #3b82f6;
    --primary-hover: #2563eb;
    --secondary: #f8fafc;
    --muted: #94a3b8;
    --border: #e2e8f0;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                   0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --header-bg: rgba(255, 255, 255, 0.8);
    --testimonial-bg: #f8fafc;
}

[data-theme="dark"] {
    /* Dark theme variables */
    --background: #0f172a;
    --foreground: #f8fafc;
    --primary: #60a5fa;
    --primary-hover: #3b82f6;
    --secondary: #1e293b;
    --muted: #64748b;
    --border: #334155;
    --card-bg: #1e293b;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2),
                   0 2px 4px -1px rgba(0, 0, 0, 0.1);
    --header-bg: rgba(15, 23, 42, 0.8);
    --testimonial-bg: #1e293b;
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    line-height: 1.5;
}

/* Header styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--header-bg);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid var(--border);
    z-index: 1000;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--foreground);
    font-weight: 700;
}

.logo-icon {
    color: var(--primary);
}

/* Navigation styles */
.nav-desktop {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--foreground);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.nav-link:hover {
    color: var(--primary);
}

/* Theme toggle button */
.theme-toggle {
    background: none;
    border: none;
    color: var(--foreground);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
}

.theme-toggle:hover {
    background-color: var(--secondary);
}

[data-theme="dark"] .sun-icon {
    display: block;
}

[data-theme="dark"] .moon-icon {
    display: none;
}

.sun-icon {
    display: none;
}

.moon-icon {
    display: block;
}

/* Hero section */
#hero {
    padding: 8rem 2rem 4rem;
    text-align: center;
    background-color: var(--background);
}

#hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--foreground);
}

#hero p {
    color: var(--muted);
    margin-bottom: 2rem;
}

/* CTA Button */
.cta-button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary);
    color: #ffffff;
    text-decoration: none;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.2s;
}

.cta-button:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
}

/* Features section */
#features {
    padding: 4rem 2rem;
    background-color: var(--background);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 2rem auto 0;
}

.feature-card {
    padding: 2rem;
    background-color: var(--card-bg);
    border-radius: 1rem;
    box-shadow: var(--card-shadow);
    text-align: center;
}

.feature-card i {
    color: var(--primary);
    font-size: 2rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: var(--foreground);
    margin-bottom: 0.5rem;
}

.feature-card p {
    color: var(--muted);
}

/* Testimonials section */
#testimonials {
    padding: 4rem 2rem;
    background-color: var(--testimonial-bg);
}

.testimonial-slider {
    display: flex;
    gap: 2rem;
    overflow-x: auto;
    padding: 2rem 0;
    scroll-snap-type: x mandatory;
}

.testimonial {
    min-width: 300px;
    padding: 2rem;
    background-color: var(--card-bg);
    border-radius: 1rem;
    box-shadow: var(--card-shadow);
    scroll-snap-align: start;
}

.testimonial img {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.testimonial blockquote {
    color: var(--foreground);
    margin-bottom: 1rem;
    font-style: italic;
}

.testimonial cite {
    color: var(--muted);
    font-style: normal;
}

/* CTA section */
#cta {
    padding: 4rem 2rem;
    text-align: center;
    background-color: var(--background);
}

#cta h2 {
    margin-bottom: 2rem;
    color: var(--foreground);
}

/* Footer */
footer {
    background-color: var(--card-bg);
    border-top: 1px solid var(--border);
    padding: 2rem;
    text-align: center;
}

.footer-links {
    margin-bottom: 1rem;
}

.footer-links a {
    color: var(--muted);
    text-decoration: none;
    margin: 0 1rem;
}

.footer-links a:hover {
    color: var(--primary);
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.social-icons a {
    color: var(--muted);
    transition: color 0.2s;
}

.social-icons a:hover {
    color: var(--primary);
}

footer p {
    color: var(--muted);
}

/* Section headers */
h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--foreground);
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-desktop {
        display: none;
    }

    #hero h1 {
        font-size: 2rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .testimonial-slider {
        padding: 1rem 0;
    }
}

/* Add these styles to your main.css or appropriate stylesheet */
/* .password-field {
    position: relative;
    width: 100%;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: var(--light-text-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: var(--text-color);
}

.password-toggle svg {
    width: 20px;
    height: 20px;
    transition: color 0.3s ease;
}

.password-toggle .eye-icon,
.password-toggle .eye-off-icon {
    position: absolute;
    transition: opacity 0.3s ease;
}

.password-toggle .hidden {
    opacity: 0;
}

.dark-mode .password-toggle {
    color: var(--light-text-color);
}

.dark-mode .password-toggle:hover {
    color: var(--text-color);
} */
/* Add these modern styles to your CSS */
.password-wrapper {
    position: relative;
    width: 100%;
}

.password-wrapper input {
    width: 100%;
    padding: 12px 40px 12px 16px; /* Increased padding for better spacing */
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--input-background);
    color: var(--text-color);
}

.password-wrapper input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    outline: none;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-text-color);
    transition: all 0.3s ease;
    border-radius: 50%;
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.password-toggle:active {
    transform: translateY(-50%) scale(0.95);
}

.password-toggle svg {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
}

.password-toggle .eye,
.password-toggle .eye-off {
    position: absolute;
    opacity: 1;
    transform-origin: center;
}

.password-toggle .hidden {
    opacity: 0;
    transform: scale(0.8);
}

/* Animation for the eye icon */
@keyframes blink {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(0.1); }
}

.password-toggle:hover .eye {
    animation: blink 1s ease infinite;
}

/* Dark mode adjustments */
.dark-mode .password-wrapper input {
    background: var(--input-background);
    border-color: var(--border-color);
}

.dark-mode .password-wrapper input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.dark-mode .password-toggle:hover {
    background: rgba(52, 152, 219, 0.15);
}

/* Focus styles for accessibility */
.password-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
}

/* Error state */
.password-wrapper.error input {
    border-color: var(--accent-color);
}

.password-wrapper.error .password-toggle {
    color: var(--accent-color);
}

/* Success state */
.password-wrapper.success input {
    border-color: #2ecc71;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .password-wrapper input {
        padding: 10px 36px 10px 12px;
        font-size: 0.95rem;
    }

    .password-toggle {
        right: 8px;
    }

    .password-toggle svg {
        width: 18px;
        height: 18px;
    }
}
