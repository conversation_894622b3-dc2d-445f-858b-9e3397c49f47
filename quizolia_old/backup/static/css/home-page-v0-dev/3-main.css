/* Base Theme Variables */
:root {
    /* Educational & Professional Color Palette */
    --primary-color: #4A90E2;    /* Trustworthy blue */
    --secondary-color: #42B883;  /* Growth green */
    --accent-color: #FF6B6B;     /* Engaging coral */
    --success-color: #2ECC71;    /* Positive green */
    --warning-color: #F1C40F;    /* Attention yellow */
    --info-color: #3498DB;       /* Information blue */
    
    /* Core UI Colors */
    --background-color: #F8F9FA;
    --card-background: #FFFFFF;
    --text-color: #2C3E50;
    --text-muted: #6C757D;
    --border-color: #E9ECEF;
    
    /* Form Elements */
    --input-background: #FFFFFF;
    --input-border: #CED4DA;
    --input-focus-border: #4A90E2;
    --input-text: #2C3E50;
    --input-placeholder: #6C757D;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 250ms ease;
    --transition-slow: 350ms ease;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --primary-color: #60A5FA;
    --secondary-color: #4ADE80;
    --accent-color: #FF8787;
    --success-color: #34D399;
    --warning-color: #FBBF24;
    --info-color: #38BDF8;
    
    --background-color: #1A1F2B;
    --card-background: #242937;
    --text-color: #E5E7EB;
    --text-muted: #9CA3AF;
    --border-color: #374151;
    
    --input-background: #2C3444;
    --input-border: #4B5563;
    --input-focus-border: #60A5FA;
    --input-text: #E5E7EB;
    --input-placeholder: #9CA3AF;
    
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.2);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.25);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.3);
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: background-color var(--transition-normal),
                color var(--transition-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-color);
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: clamp(2rem, 5vw, 3.5rem); }
h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }

p {
    margin-bottom: 1rem;
    color: var(--text-muted);
}

/* Layout Components */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header & Navigation */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--card-background);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    transition: transform var(--transition-normal),
                background-color var(--transition-normal);
}

header.scroll-up {
    transform: translateY(0);
}

header.scroll-down {
    transform: translateY(-100%);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--text-color);
    font-weight: 700;
    font-size: 1.5rem;
    transition: transform var(--transition-fast);
}

.logo:hover {
    transform: scale(1.05);
}

.logo-icon {
    color: var(--primary-color);
    transition: transform var(--transition-slow);
}

.logo:hover .logo-icon {
    transform: rotate(360deg);
}

/* Navigation Links */
.nav-desktop {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

/* Buttons */
.cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background-color: var(--info-color);
}

.cta-button:active {
    transform: translateY(0);
}

/* Form Elements */
.form-container {
    background-color: var(--card-background);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    margin: 2rem auto;
    transition: transform var(--transition-normal),
                box-shadow var(--transition-normal);
}

.form-container:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

input[type="text"],
input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--input-border);
    border-radius: 8px;
    background-color: var(--input-background);
    color: var(--input-text);
    transition: all var(--transition-normal);
}

input:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* Password Field Styles */
.password-wrapper {
    position: relative;
    width: 100%;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: var(--text-muted);
    transition: all var(--transition-normal);
    border-radius: 50%;
}

.password-toggle:hover {
    color: var(--primary-color);
    background-color: rgba(74, 144, 226, 0.1);
}

.password-toggle svg {
    width: 20px;
    height: 20px;
    transition: all var(--transition-normal);
}

.password-toggle .eye,
.password-toggle .eye-off {
    position: absolute;
    opacity: 1;
    transform-origin: center;
}

.password-toggle .hidden {
    opacity: 0;
    transform: scale(0.8);
}

/* Validation States */
input.valid {
    border-color: var(--success-color);
}

input.invalid {
    border-color: var(--accent-color);
}

.error-message {
    color: var(--accent-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.error-message.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Theme Toggle */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.theme-toggle:hover {
    background-color: rgba(74, 144, 226, 0.1);
    transform: rotate(15deg);
}

.theme-toggle svg {
    width: 24px;
    height: 24px;
}

/* Landing Page Specific */
.hero-section {
    padding: 8rem 2rem 4rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(
        circle at top right,
        rgba(74, 144, 226, 0.1),
        transparent 70%
    );
    pointer-events: none;
}

/* Features Section */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-card {
    background-color: var(--card-background);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

/* Testimonials */
.testimonial-slider {
    display: flex;
    gap: 2rem;
    overflow-x: auto;
    padding: 2rem 0;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
}

.testimonial-slider::-webkit-scrollbar {
    display: none;
}

.testimonial {
    min-width: 300px;
    background-color: var(--card-background);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    scroll-snap-align: start;
    transition: all var(--transition-normal);
}

.testimonial:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Footer */
footer {
    background-color: var(--card-background);
    padding: 4rem 2rem;
    margin-top: 4rem;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-links a {
    color: var(--text-muted);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-desktop {
        display: none;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .hero-section {
        padding: 6rem 1rem 3rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .form-container {
        margin: 1rem;
        padding: 1.5rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
}

:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .no-print {
        display: none;
    }
    
    body {
        background: white;
    }
    
    .container {
        width: 100%;
        max-width: none;
    }
}