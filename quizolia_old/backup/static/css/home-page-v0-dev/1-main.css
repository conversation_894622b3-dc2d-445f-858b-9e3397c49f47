/* Theme System */
:root {
    /* Education-Focused Color Palette - Light Theme */
    --primary: #4361EE;    /* Engaging Blue */
    --secondary: #4CC9F0;  /* <PERSON>an */
    --accent: #F72585;     /* Vibrant Pink */
    --success: #06D6A0;    /* Fresh Mint */
    --warning: #FFD60A;    /* Sunny Yellow */
    --info: #118AB2;       /* Ocean Blue */
    
    /* UI Colors */
    --background: #F8F9FC;
    --foreground: #2B2D42;
    --muted: #64748B;
    --border: #E2E8F0;
    
    /* Component Colors */
    --card-bg: #FFFFFF;
    --input-bg: #FFFFFF;
    --header-bg: rgba(255, 255, 255, 0.95);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary), var(--secondary));
    --gradient-accent: linear-gradient(135deg, var(--accent), var(--primary));
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(43, 45, 66, 0.05);
    --shadow-md: 0 4px 6px rgba(43, 45, 66, 0.07);
    --shadow-lg: 0 10px 15px rgba(43, 45, 66, 0.1);
    
    /* Animations */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  [data-theme="dark"] {
    --primary: #4895EF;
    --secondary: #56CFE1;
    --accent: #F72585;
    --success: #06D6A0;
    --warning: #FFD60A;
    --info: #118AB2;
    
    --background: #0F172A;
    --foreground: #F8FAFC;
    --muted: #94A3B8;
    --border: #1E293B;
    
    --card-bg: #1E293B;
    --input-bg: #2C3444;
    --header-bg: rgba(15, 23, 42, 0.95);
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  }
  
  /* Reset & Base */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    line-height: 1.6;
    transition: background-color var(--transition-normal),
                color var(--transition-normal);
  }
  
  /* Modern Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes fadeScale {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Header & Navigation */
  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--header-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border);
    z-index: 1000;
    animation: slideUp 0.5s ease-out;
  }
  
  .logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground);
    text-decoration: none;
    transition: transform var(--transition-fast);
  }
  
  .logo:hover {
    transform: scale(1.05);
  }
  
  .logo-icon {
    color: var(--primary);
    animation: float 3s ease-in-out infinite;
  }
  
  /* Hero Section */
  #hero {
    background: var(--gradient-primary);
    padding: 8rem 2rem 6rem;
    text-align: center;
    position: relative;
    overflow: hidden;
  }
  
  #hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.1;
  }
  
  #hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: white;
    margin-bottom: 1.5rem;
    animation: slideUp 0.8s ease-out;
  }
  
  #hero p {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto 2.5rem;
    animation: slideUp 0.8s ease-out 0.2s backwards;
  }
  
  /* Feature Cards */
  .feature-card {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    animation: fadeScale 0.5s ease-out;
    position: relative;
    overflow: hidden;
  }
  
  .feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-normal);
  }
  
  .feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }
  
  .feature-card:hover::before {
    transform: scaleX(1);
  }
  
  .feature-card i {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 1.5rem;
    transition: transform var(--transition-normal);
  }
  
  .feature-card:hover i {
    transform: scale(1.1) rotate(5deg);
  }
  
  /* Testimonials */
  .testimonial {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    animation: fadeScale 0.5s ease-out;
  }
  
  .testimonial img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid var(--primary);
    padding: 3px;
    transition: transform var(--transition-normal);
  }
  
  .testimonial:hover img {
    transform: scale(1.1);
  }
  
  /* CTA Button */
  .cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.875rem 2rem;
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    border-radius: 12px;
    text-decoration: none;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  
  .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-accent);
    opacity: 0;
    transition: opacity var(--transition-normal);
  }
  
  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
  }
  
  .cta-button:hover::before {
    opacity: 1;
  }
  
  .cta-button span {
    position: relative;
    z-index: 1;
  }
  
  /* Theme Toggle */
  .theme-toggle {
    background: none;
    border: none;
    color: var(--foreground);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-normal);
  }
  
  .theme-toggle:hover {
    background-color: rgba(67, 97, 238, 0.1);
    transform: rotate(15deg);
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .nav-desktop {
      display: none;
    }
    
    .container {
      padding: 0 1rem;
    }
    
    .feature-grid {
      grid-template-columns: 1fr;
    }
  }
  
  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    *,
    ::before,
    ::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
  
  :focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }