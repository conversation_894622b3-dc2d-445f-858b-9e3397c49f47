/* Theme System */
:root {
    /* Core Colors */
    --primary: #4A90E2;
    --secondary: #42B883;
    --accent: #FF6B6B;
    --success: #2ECC71;
    --warning: #F1C40F;
    --info: #3498DB;
  
    /* UI Colors */
    --background: #F8F9FA;
    --foreground: #2C3E50;
    --muted: #6C757D;
    --border: #E9ECEF;
    
    /* Component Colors */
    --card-bg: #FFFFFF;
    --input-bg: #FFFFFF;
    --header-bg: rgba(255, 255, 255, 0.9);
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
    /* Animation */
    --transition-fast: 150ms ease;
    --transition-normal: 250ms ease;
    --transition-slow: 350ms ease;
  }
  
  [data-theme="dark"] {
    --primary: #60A5FA;
    --secondary: #4ADE80;
    --accent: #FF8787;
    --success: #34D399;
    --warning: #FBBF24;
    --info: #38BDF8;
  
    --background: #1A1F2B;
    --foreground: #E5E7EB;
    --muted: #9CA3AF;
    --border: #374151;
    
    --card-bg: #242937;
    --input-bg: #2C3444;
    --header-bg: rgba(26, 31, 43, 0.9);
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  }
  
  /* Reset & Base */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    line-height: 1.6;
    transition: background-color var(--transition-normal),
                color var(--transition-normal);
  }
  
  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    color: var(--foreground);
    line-height: 1.2;
    margin-bottom: 1rem;
  }
  
  h1 { font-size: clamp(2rem, 5vw, 3.5rem); }
  h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
  h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
  
  p {
    margin-bottom: 1rem;
    color: var(--muted);
  }
  
  /* Layout */
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  section {
    padding: 4rem 2rem;
  }
  
  /* Header & Navigation */
  header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--header-bg);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid var(--border);
    z-index: 1000;
    transition: transform var(--transition-normal);
  }
  
  nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground);
    text-decoration: none;
    transition: transform var(--transition-fast);
  }
  
  .logo:hover {
    transform: scale(1.05);
  }
  
  .logo-icon {
    color: var(--primary);
    transition: transform var(--transition-slow);
  }
  
  .logo:hover .logo-icon {
    transform: rotate(360deg);
  }
  
  .nav-desktop {
    display: flex;
    gap: 2rem;
    align-items: center;
  }
  
  .nav-link {
    color: var(--foreground);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
  }
  
  .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary);
    transition: width var(--transition-normal);
  }
  
  .nav-link:hover::after {
    width: 100%;
  }
  
  /* Buttons */
  .cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
  }
  
  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background-color: var(--info);
  }
  
  .cta-button:active {
    transform: translateY(0);
  }
  
  /* Forms */
  .form-container {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    margin: 2rem auto;
    transition: transform var(--transition-normal),
                box-shadow var(--transition-normal);
  }
  
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--foreground);
    font-weight: 500;
  }
  
  input[type="text"],
  input[type="email"],
  input[type="password"] {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border);
    border-radius: 8px;
    background-color: var(--input-bg);
    color: var(--foreground);
    transition: all var(--transition-normal);
  }
  
  input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
  }
  
  /* Password Field */
  .password-wrapper {
    position: relative;
    width: 100%;
  }
  
  .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: var(--muted);
    border-radius: 50%;
    transition: all var(--transition-normal);
  }
  
  .password-toggle:hover {
    color: var(--primary);
    background-color: rgba(74, 144, 226, 0.1);
  }
  
  .password-toggle svg {
    width: 20px;
    height: 20px;
    transition: all var(--transition-normal);
  }
  
  .password-toggle .eye,
  .password-toggle .eye-off {
    position: absolute;
    opacity: 1;
    transform-origin: center;
  }
  
  .password-toggle .hidden {
    opacity: 0;
    transform: scale(0.8);
  }
  
  /* Theme Toggle */
  .theme-toggle {
    background: none;
    border: none;
    color: var(--foreground);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-normal);
  }
  
  .theme-toggle:hover {
    background-color: rgba(74, 144, 226, 0.1);
    transform: rotate(15deg);
  }
  
  /* Features */
  .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
  }
  
  .feature-card {
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
  }
  
  .feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary), var(--secondary));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-normal);
  }
  
  .feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }
  
  .feature-card:hover::before {
    transform: scaleX(1);
  }
  
  /* Testimonials */
  .testimonial-slider {
    display: flex;
    gap: 2rem;
    overflow-x: auto;
    padding: 2rem 0;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
  }
  
  .testimonial-slider::-webkit-scrollbar {
    display: none;
  }
  
  .testimonial {
    min-width: 300px;
    background-color: var(--card-bg);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    scroll-snap-align: start;
    transition: all var(--transition-normal);
  }
  
  .testimonial:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }
  
  /* Footer */
  footer {
    background-color: var(--card-bg);
    padding: 4rem 2rem;
    margin-top: 4rem;
    border-top: 1px solid var(--border);
  }
  
  .footer-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .footer-links a {
    color: var(--muted);
    text-decoration: none;
    transition: color var(--transition-normal);
  }
  
  .footer-links a:hover {
    color: var(--primary);
  }
  
  /* Animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .nav-desktop {
      display: none;
    }
    
    section {
      padding: 3rem 1rem;
    }
    
    .feature-grid {
      grid-template-columns: 1fr;
    }
    
    .form-container {
      margin: 1rem;
      padding: 1.5rem;
    }
  }
  
  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation: none !important;
      transition: none !important;
    }
  }
  
  :focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
  
  /* Print Styles */
  @media print {
    .no-print {
      display: none;
    }
    
    body {
      background: white;
    }
    
    .container {
      width: 100%;
      max-width: none;
    }
  }