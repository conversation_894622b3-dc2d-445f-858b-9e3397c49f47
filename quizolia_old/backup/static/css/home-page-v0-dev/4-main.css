:root {
    /* Educational color palette - Light theme */
    --primary-color: #4A90E2;  /* Trustworthy blue */
    --secondary-color: #42B883; /* Growth green */
    --accent-color: #FF6B6B;   /* Engaging coral */
    --success-color: #2ECC71;  /* Positive green */
    --warning-color: #F1C40F;  /* Attention yellow */
    --info-color: #3498DB;     /* Information blue */
    
    /* Background and text colors */
    --background: #FFFFFF;
    --foreground: #2C3E50;
    --muted: #94A3B8;
    --light-muted: #E2E8F0;
    
    /* Card and container styles */
    --card-bg: #FFFFFF;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                   0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --header-bg: rgba(255, 255, 255, 0.9);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #4A90E2, #42B883);
    --gradient-secondary: linear-gradient(135deg, #42B883, #3498DB);
}

[data-theme="dark"] {
    /* Dark theme variations */
    --primary-color: #60A5FA;
    --secondary-color: #4ADE80;
    --accent-color: #FF8787;
    --success-color: #34D399;
    --warning-color: #FBBF24;
    --info-color: #38BDF8;
    
    --background: #0F172A;
    --foreground: #F8FAFC;
    --muted: #64748B;
    --light-muted: #1E293B;
    
    --card-bg: #1E293B;
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2),
                   0 2px 4px -1px rgba(0, 0, 0, 0.1);
    --header-bg: rgba(15, 23, 42, 0.9);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Enhanced Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--header-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--light-muted);
    z-index: 1000;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.header.scroll-up {
    transform: translateY(0);
}

.header.scroll-down {
    transform: translateY(-100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Enhanced Logo Animation */
.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--foreground);
    font-weight: 700;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

.logo-icon {
    color: var(--primary-color);
    transition: transform 0.5s ease;
}

.logo:hover .logo-icon {
    transform: rotate(360deg);
}

/* Navigation Styles */
.nav-desktop {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--foreground);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Enhanced CTA Button */
.cta-button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-image: var(--gradient-primary);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: var(--gradient-secondary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.cta-button:hover::before {
    opacity: 1;
}

.cta-button span {
    position: relative;
    z-index: 1;
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: none;
    color: var(--foreground);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.theme-toggle:hover {
    background-color: var(--light-muted);
    transform: rotate(15deg);
}

/* Hero Section */
#hero {
    padding: 8rem 2rem 6rem;
    text-align: center;
    background: var(--background);
    position: relative;
    overflow: hidden;
}

#hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(circle at top right, 
                rgba(74, 144, 226, 0.1) 0%,
                transparent 70%);
    pointer-events: none;
}

#hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    color: var(--foreground);
    line-height: 1.2;
    animation: fadeInUp 1s ease;
}

#hero p {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    color: var(--muted);
    max-width: 600px;
    margin: 0 auto 2.5rem;
    animation: fadeInUp 1s ease 0.2s backwards;
}

/* Features Section */
#features {
    padding: 6rem 2rem;
    background-color: var(--background);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
    max-width: 1200px;
    margin: 3rem auto 0;
}

.feature-card {
    padding: 2.5rem;
    background-color: var(--card-bg);
    border-radius: 1rem;
    box-shadow: var(--card-shadow);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.feature-card:hover i {
    transform: scale(1.1);
}

/* Testimonials Section */
#testimonials {
    padding: 6rem 2rem;
    background-color: var(--background);
    position: relative;
}

.testimonial-slider {
    display: flex;
    gap: 2rem;
    overflow-x: auto;
    padding: 2rem 0;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
}

.testimonial-slider::-webkit-scrollbar {
    display: none;
}

.testimonial {
    min-width: 300px;
    padding: 2rem;
    background-color: var(--card-bg);
    border-radius: 1rem;
    box-shadow: var(--card-shadow);
    scroll-snap-align: start;
    transition: transform 0.3s ease;
}

.testimonial:hover {
    transform: translateY(-5px);
}

.testimonial img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 1.5rem;
    border: 3px solid var(--primary-color);
    padding: 3px;
    transition: transform 0.3s ease;
}

.testimonial:hover img {
    transform: scale(1.1);
}

.testimonial blockquote {
    color: var(--foreground);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    position: relative;
    padding: 0 1rem;
}

.testimonial blockquote::before,
.testimonial blockquote::after {
    content: '"';
    color: var(--primary-color);
    font-size: 3rem;
    position: absolute;
    opacity: 0.2;
}

.testimonial blockquote::before {
    left: -1rem;
    top: -1rem;
}

.testimonial blockquote::after {
    right: -1rem;
    bottom: -2rem;
}

/* CTA Section */
#cta {
    padding: 6rem 2rem;
    text-align: center;
    background: var(--background);
    position: relative;
    overflow: hidden;
}

#cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center,
                rgba(74, 144, 226, 0.1) 0%,
                transparent 70%);
    pointer-events: none;
}

/* Footer */
footer {
    background-color: var(--card-bg);
    border-top: 1px solid var(--light-muted);
    padding: 4rem 2rem;
    text-align: center;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--muted);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.footer-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-links a:hover::after {
    transform: scaleX(1);
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.social-icons a {
    color: var(--muted);
    transition: all 0.3s ease;
}

.social-icons a:hover {
    color: var(--primary-color);
    transform: translateY(-3px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-desktop {
        display: none;
    }

    .container {
        padding: 1rem;
    }

    #hero {
        padding: 6rem 1rem 4rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonial-slider {
        padding: 1rem 0;
    }

    .footer-links {
        gap: 1rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
}

/* Focus styles */
:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading animations */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}