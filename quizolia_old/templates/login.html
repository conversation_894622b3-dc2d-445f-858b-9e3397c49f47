<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login To Your Dashboard | Quizolia</title>
    <link rel="icon" href="{{ url_for('static', filename='images/home-favicon.jpg') }}" type="image/jpg">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/login.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <a href="/" class="logo">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="logo-icon">
                        <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313-12.454z"></path>
                        <path d="M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2"></path>
                        <path d="M19 11h2m-1 -1v2"></path>
                    </svg>
                    <span>Quizolia</span>
                </a>
            </div>
            <ul>
                <li><a href="/signup" class="cta-button">Get Started</a></li>
            </ul>
        </nav>
    </header>
    <div class="container">
        <div class="split-screen">
            <div class="left">
                <div class="content">
                    <h1>Welcome Back!</h1>
                    <p>Ready to Be A Part Of Something Amazing?</p>
                    <img src="{{ url_for('static', filename='images/illustration.jpg') }}" alt="Illustration" class="illustration">
                </div>
            </div>
            <div class="right">
                <div class="login-container">
                    <div class="login-header">
                        <h2>Login</h2>
                        <button id="darkModeToggle" class="dark-mode-toggle" aria-label="Toggle dark mode">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="sun"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="moon"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>
                        </button>
                    </div>
                    <form action="/login" method="post" class="form__container">
                        <div class="form__labelinput">
                            <label>E-mail</label>
                            <input name="email" type="email" placeholder="Email Address" required />
                        </div>
                        <div class="error-message"></div>

                        <!-- div class="form__labelinput">
                            <label>Password</label>
                            <input name="password" type="password" placeholder="Password" required />
                        </!-->
                        <!-- div class="form__labelinput">
                            <label>Password</label>
                            <div class="password-field">
                                <input name="password" type="password" placeholder="Password" required />
                                <button type="button" class="password-toggle" aria-label="Toggle password visibility">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                        <circle cx="12" cy="12" r="3"></circle>
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="eye-off-icon hidden" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                                        <line x1="1" y1="1" x2="23" y2="23"></line>
                                    </svg>
                                </button>
                            </div>
                        </!-->
                        <div class="form__labelinput">
                            <label for="password">Password</label>
                            <div class="password-wrapper">
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    placeholder="Enter your password" 
                                    required 
                                />
                                <button 
                                    type="button" 
                                    class="password-toggle" 
                                    aria-label="Toggle password visibility"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="eye" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="eye-off hidden" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                                        <line x1="2" y1="2" x2="22" y2="22"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="form__submit">
                            <button type="submit">Login</button>
                            <p>Don't Have An Account? <a href="/signup">Sign Up</a></p>
                        </div>
                    </form>
                    <!-- div class="forgot-password">
                        <a href="#">Forgot password?</a>
                    </div 
                    <div class="signup-prompt">
                        <p>Don't have an account? <a href="/signup">Sign up here</a></p>
                    </div -->
                    <div div class="language-selector">
                        <select id="languageSelect">
                            <option value="en">English</option>
                            <option value="es">Español</option>
                            <option value="fr">Français</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer>
        <p>&copy; 2025 Quizolia. <a href="#">Terms of Service</a> | <a href="#">Privacy Policy</a> | <a href="https://github.com/Quizolia" target="_blank">Contact Support</a></p>
    </footer>
    <script src="{{ url_for('static', filename='js/login.js') }}"></script>
</body>
</html>
