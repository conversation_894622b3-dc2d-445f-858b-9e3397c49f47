<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coming Soon | Quizolia</title>
    <link rel="icon" href="{{ url_for('static', filename='images/home-favicon.jpg') }}" type="image/jpg">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Light theme */
            --background: #ffffff;
            --foreground: #0f172a;
            --primary: #3b82f6;
            --primary-foreground: #ffffff;
            --muted: #f1f5f9;
            --muted-foreground: #64748b;
            --border: #e2e8f0;
            --accent: #8b5cf6;
        }

        .dark {
            /* Dark theme */
            --background: #0f172a;
            --foreground: #f8fafc;
            --primary: #60a5fa;
            --primary-foreground: #0f172a;
            --muted: #1e293b;
            --muted-foreground: #94a3b8;
            --border: #1e293b;
            --accent: #a78bfa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background);
            color: var(--foreground);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            transition: background-color 0.3s, color 0.3s;
        }

        .header {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            color: var(--foreground);
            font-weight: 700;
        }

        .logo-icon {
            color: var(--primary);
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--foreground);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.5rem;
            transition: background-color 0.2s;
        }

        .theme-toggle:hover {
            background-color: var(--muted);
        }

        .dark .sun-icon {
            display: block;
        }

        .dark .moon-icon {
            display: none;
        }

        .sun-icon {
            display: none;
        }

        .moon-icon {
            display: block;
        }

        .main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .coming-soon-container {
            text-align: center;
            max-width: 28rem;
            margin: 0 auto;
        }

        .coming-soon-icon {
            width: 160px;
            height: 160px;
            margin: 0 auto 2rem;
            position: relative;
            animation: float 6s ease-in-out infinite;
        }

        .coming-soon-icon svg {
            width: 100%;
            height: 100%;
            color: var(--accent);
        }

        .coming-soon-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shimmer 2s linear infinite;
        }

        .coming-soon-message {
            color: var(--muted-foreground);
            font-size: 1.125rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0) rotate(0);
            }
            25% {
                transform: translateY(-15px) rotate(-5deg);
            }
            75% {
                transform: translateY(-15px) rotate(5deg);
            }
        }

        @keyframes shimmer {
            from {
                background-position: 200% center;
            }
        }

        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--accent);
            border-radius: 50%;
            animation: particle 2s infinite;
        }

        @keyframes particle {
            0% {
                transform: translate(0, 0);
                opacity: 1;
            }
            100% {
                transform: translate(var(--tx), var(--ty));
                opacity: 0;
            }
        }

        @media (max-width: 640px) {
            .coming-soon-icon {
                width: 120px;
                height: 120px;
            }

            .coming-soon-title {
                font-size: 1.5rem;
            }

            .coming-soon-message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="/" class="logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="logo-icon">
                    <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313-12.454z"></path>
                    <path d="M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2"></path>
                    <path d="M19 11h2m-1 -1v2"></path>
                </svg>
                <span>Quizolia</span>
            </a>
            <button class="theme-toggle" aria-label="Toggle theme">
                <svg class="sun-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="5"></circle>
                    <line x1="12" y1="1" x2="12" y2="3"></line>
                    <line x1="12" y1="21" x2="12" y2="23"></line>
                    <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                    <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                    <line x1="1" y1="12" x2="3" y2="12"></line>
                    <line x1="21" y1="12" x2="23" y2="12"></line>
                    <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                    <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>
                <svg class="moon-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                </svg>
            </button>
        </div>
    </header>

    <main class="main">
        <div class="coming-soon-container">
            <div class="coming-soon-icon">
                <div class="particles" id="particles"></div>
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2v4"/>
                    <path d="M12 18v4"/>
                    <path d="M4.93 4.93l2.83 2.83"/>
                    <path d="M16.24 16.24l2.83 2.83"/>
                    <path d="M2 12h4"/>
                    <path d="M18 12h4"/>
                    <path d="M4.93 19.07l2.83-2.83"/>
                    <path d="M16.24 7.76l2.83-2.83"/>
                </svg>
            </div>
            <h1 class="coming-soon-title">Coming Soon</h1>
            <p class="coming-soon-message">We're building this feature. Stay tuned!</p>
        </div>
    </main>

    <script>
        // Theme toggle functionality
        const themeToggle = document.querySelector('.theme-toggle');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');

        // Check for saved theme preference or use system preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark' || (!savedTheme && prefersDark.matches)) {
            document.body.classList.add('dark');
        }

        // Theme toggle click handler
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark');
            localStorage.setItem('theme', 
                document.body.classList.contains('dark') ? 'dark' : 'light'
            );
        });

        // Listen for system theme changes
        prefersDark.addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                if (e.matches) {
                    document.body.classList.add('dark');
                } else {
                    document.body.classList.remove('dark');
                }
            }
        });

        // Particle animation
        function createParticles() {
            const container = document.getElementById('particles');
            const particleCount = 12;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random position around the icon
                const angle = (i / particleCount) * Math.PI * 2;
                const distance = 40 + Math.random() * 20;

                particle.style.setProperty('--tx', `${Math.cos(angle) * distance}px`);
                particle.style.setProperty('--ty', `${Math.sin(angle) * distance}px`);

                // Random delay
                particle.style.animationDelay = `${Math.random() * 2}s`;

                container.appendChild(particle);
            }
        }

        // Initialize particles
        createParticles();
    </script>
</body>
</html>