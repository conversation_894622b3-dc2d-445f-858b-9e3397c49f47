#!/usr/bin/env python3
"""
Test script to verify the Quizolia API is working correctly
"""

import sys
import os
import requests
import json


def test_api():
    """Test basic API functionality"""
    base_url = "http://localhost:5000"

    print("🧪 Testing Quizolia API...")

    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
        return False

    # Test 2: API info
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ API info endpoint working")
        else:
            print(f"❌ API info endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ API info endpoint failed: {e}")

    # Test 3: Swagger documentation
    try:
        response = requests.get(f"{base_url}/swagger/", timeout=5)
        if response.status_code == 200:
            print("✅ Swagger documentation accessible")
        else:
            print(f"⚠️  Swagger documentation: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Swagger documentation: {e}")

    print("✅ Basic API tests completed!")
    return True


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Test with custom URL
        base_url = sys.argv[1]

    success = test_api()
    sys.exit(0 if success else 1)
