#!/usr/bin/env python3
"""
Startup script for the Quizolia API
This script initializes the database and starts the Flask application.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Flask app
from api_app import app, db

def initialize_database():
    """Initialize the database tables"""
    try:
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully!")
            return True
    except Exception as e:
        print(f"❌ Error creating database tables: {e}")
        return False

def main():
    """Main function to start the API"""
    print("🚀 Starting Quizolia API Server...")
    
    # Check if required environment variables are set
    required_vars = ['SQLALCHEMY_DATABASE_URI', 'SECRET_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please create a .env file with the required variables.")
        return 1
    
    # Initialize database
    if not initialize_database():
        return 1
    
    # Start the Flask application
    try:
        port = int(os.environ.get("PORT", 5000))
        debug = os.environ.get("FLASK_ENV") == "development"
        
        print(f"🌐 API Server starting on http://localhost:{port}")
        print(f"📚 Swagger Documentation: http://localhost:{port}/swagger/")
        print(f"🔍 Health Check: http://localhost:{port}/health")
        print("\nPress Ctrl+C to stop the server")
        
        app.run(host="0.0.0.0", port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return 0
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
