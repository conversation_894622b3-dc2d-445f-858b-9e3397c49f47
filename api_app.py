from flask import Flask, request, jsonify, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_restx import Api, Resource, fields, Namespace
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import jwt
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta
import traceback

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('SQLALCHEMY_DATABASE_URI')
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(
    minutes=15)  # Short-lived access token
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(
    days=30)   # Long-lived refresh token

# Initialize extensions
db = SQLAlchemy(app)
CORS(app, supports_credentials=True, origins=[
     'http://localhost:3000', 'http://127.0.0.1:3000'])

# Initialize Flask-RESTX for Swagger
api = Api(app,
          version='1.0',
          title='Quizolia API',
          description='A comprehensive quiz application API with JWT authentication',
          doc='/swagger/',
          prefix='/api'
          )

# User model


class Users(db.Model):
    """
    The database model for users
    """
    __tablename__ = 'users'
    userId = db.Column(db.Integer, primary_key=True, autoincrement=True)
    firstname = db.Column(db.String(50), nullable=False)
    lastname = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(100), nullable=False, unique=True)
    password = db.Column(db.String(255), nullable=False)
    created_at = db.Column(
        db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow,
                           onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)

    def to_dict(self):
        """Convert user object to dictionary"""
        return {
            'userId': self.userId,
            'firstname': self.firstname,
            'lastname': self.lastname,
            'email': self.email,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

# JWT Utility Functions


def generate_access_token(user_id):
    """Generate JWT access token"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + app.config['JWT_ACCESS_TOKEN_EXPIRES'],
        'iat': datetime.utcnow(),
        'type': 'access'
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')


def generate_refresh_token(user_id):
    """Generate JWT refresh token"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + app.config['JWT_REFRESH_TOKEN_EXPIRES'],
        'iat': datetime.utcnow(),
        'type': 'refresh'
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')


def verify_token(token, token_type='access'):
    """Verify JWT token"""
    try:
        payload = jwt.decode(
            token, app.config['SECRET_KEY'], algorithms=['HS256'])
        if payload.get('type') != token_type:
            return None
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

# Authentication decorator


def jwt_required(f):
    """Decorator to require JWT authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        access_token = request.cookies.get('access_token')

        if not access_token:
            return jsonify({'error': 'Access token missing', 'code': 'TOKEN_MISSING'}), 401

        payload = verify_token(access_token, 'access')
        if not payload:
            return jsonify({'error': 'Invalid or expired token', 'code': 'TOKEN_INVALID'}), 401

        # Get user from database
        user = Users.query.get(payload['user_id'])
        if not user:
            return jsonify({'error': 'User not found', 'code': 'USER_NOT_FOUND'}), 401

        # Add user to request context
        request.current_user = user
        return f(*args, **kwargs)

    return decorated_function

# API Response utilities


def success_response(data=None, message="Success", status_code=200):
    """Standard success response"""
    response = {
        'success': True,
        'message': message,
        'data': data
    }
    return jsonify(response), status_code


def error_response(message="Error", code="UNKNOWN_ERROR", status_code=400):
    """Standard error response"""
    response = {
        'success': False,
        'error': message,
        'code': code
    }
    return jsonify(response), status_code


# Create namespaces
auth_ns = Namespace('auth', description='Authentication operations')
user_ns = Namespace('user', description='User operations')
quiz_ns = Namespace('quiz', description='Quiz operations')

api.add_namespace(auth_ns)
api.add_namespace(user_ns)
api.add_namespace(quiz_ns)

# API Models for Swagger documentation
signup_model = api.model('Signup', {
    'firstname': fields.String(required=True, description='First name'),
    'lastname': fields.String(required=True, description='Last name'),
    'email': fields.String(required=True, description='Email address'),
    'password': fields.String(required=True, description='Password')
})

login_model = api.model('Login', {
    'email': fields.String(required=True, description='Email address'),
    'password': fields.String(required=True, description='Password')
})

user_model = api.model('User', {
    'userId': fields.Integer(description='User ID'),
    'firstname': fields.String(description='First name'),
    'lastname': fields.String(description='Last name'),
    'email': fields.String(description='Email address'),
    'created_at': fields.String(description='Creation timestamp'),
    'updated_at': fields.String(description='Last update timestamp'),
    'last_login': fields.String(description='Last login timestamp')
})

profile_update_model = api.model('ProfileUpdate', {
    'firstname': fields.String(description='First name'),
    'lastname': fields.String(description='Last name'),
    'email': fields.String(description='Email address')
})

# Authentication Routes


@auth_ns.route('/signup')
class Signup(Resource):
    @auth_ns.expect(signup_model)
    @auth_ns.doc('signup_user')
    def post(self):
        """Register a new user"""
        try:
            data = request.get_json()

            # Validate required fields
            required_fields = ['firstname', 'lastname', 'email', 'password']
            for field in required_fields:
                if not data.get(field):
                    return error_response(f'{field} is required', 'MISSING_FIELD', 400)

            # Check if user already exists
            if Users.query.filter_by(email=data['email']).first():
                return error_response('Email already registered', 'EMAIL_EXISTS', 409)

            # Create new user
            hashed_password = generate_password_hash(data['password'])
            new_user = Users(
                firstname=data['firstname'],
                lastname=data['lastname'],
                email=data['email'],
                password=hashed_password,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                last_login=datetime.utcnow()
            )

            db.session.add(new_user)
            db.session.commit()

            return success_response(
                data={'user': new_user.to_dict()},
                message='User registered successfully',
                status_code=201
            )

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Signup error: {str(e)}")
            return error_response('Registration failed', 'REGISTRATION_ERROR', 500)


@auth_ns.route('/login')
class Login(Resource):
    @auth_ns.expect(login_model)
    @auth_ns.doc('login_user')
    def post(self):
        """Authenticate user and set JWT cookies"""
        try:
            data = request.get_json()

            if not data.get('email') or not data.get('password'):
                return error_response('Email and password are required', 'MISSING_CREDENTIALS', 400)

            # Find user
            user = Users.query.filter_by(email=data['email']).first()

            if not user or not check_password_hash(user.password, data['password']):
                return error_response('Invalid email or password', 'INVALID_CREDENTIALS', 401)

            # Update last login
            user.last_login = datetime.utcnow()
            db.session.commit()

            # Generate tokens
            access_token = generate_access_token(user.userId)
            refresh_token = generate_refresh_token(user.userId)

            # Create response
            response_data = {
                'user': user.to_dict(),
                'message': 'Login successful'
            }

            response = make_response(jsonify({
                'success': True,
                'message': 'Login successful',
                'data': response_data
            }))

            # Set HTTP-only cookies
            response.set_cookie(
                'access_token',
                access_token,
                max_age=int(
                    app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds()),
                httponly=True,
                secure=False,  # Set to True in production with HTTPS
                samesite='Lax'
            )

            response.set_cookie(
                'refresh_token',
                refresh_token,
                max_age=int(
                    app.config['JWT_REFRESH_TOKEN_EXPIRES'].total_seconds()),
                httponly=True,
                secure=False,  # Set to True in production with HTTPS
                samesite='Lax'
            )

            return response

        except Exception as e:
            app.logger.error(f"Login error: {str(e)}")
            return error_response('Login failed', 'LOGIN_ERROR', 500)


@auth_ns.route('/logout')
class Logout(Resource):
    @auth_ns.doc('logout_user')
    def post(self):
        """Logout user and clear JWT cookies"""
        response = make_response(jsonify({
            'success': True,
            'message': 'Logged out successfully'
        }))

        # Clear cookies
        response.set_cookie('access_token', '', expires=0, httponly=True)
        response.set_cookie('refresh_token', '', expires=0, httponly=True)

        return response


@auth_ns.route('/refresh')
class RefreshToken(Resource):
    @auth_ns.doc('refresh_token')
    def post(self):
        """Refresh access token using refresh token"""
        try:
            refresh_token = request.cookies.get('refresh_token')

            if not refresh_token:
                return error_response('Refresh token missing', 'REFRESH_TOKEN_MISSING', 401)

            payload = verify_token(refresh_token, 'refresh')
            if not payload:
                return error_response('Invalid or expired refresh token', 'REFRESH_TOKEN_INVALID', 401)

            # Get user
            user = Users.query.get(payload['user_id'])
            if not user:
                return error_response('User not found', 'USER_NOT_FOUND', 401)

            # Generate new access token
            new_access_token = generate_access_token(user.userId)

            response = make_response(jsonify({
                'success': True,
                'message': 'Token refreshed successfully'
            }))

            # Set new access token cookie
            response.set_cookie(
                'access_token',
                new_access_token,
                max_age=int(
                    app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds()),
                httponly=True,
                secure=False,  # Set to True in production with HTTPS
                samesite='Lax'
            )

            return response

        except Exception as e:
            app.logger.error(f"Token refresh error: {str(e)}")
            return error_response('Token refresh failed', 'REFRESH_ERROR', 500)


@auth_ns.route('/verify')
class VerifyToken(Resource):
    @auth_ns.doc('verify_token')
    @jwt_required
    def get(self):
        """Verify current access token and return user info"""
        return success_response(
            data={'user': request.current_user.to_dict()},
            message='Token is valid'
        )

# User Routes


@user_ns.route('/profile')
class UserProfile(Resource):
    @user_ns.doc('get_user_profile')
    @user_ns.marshal_with(user_model)
    @jwt_required
    def get(self):
        """Get current user profile"""
        return success_response(
            data={'user': request.current_user.to_dict()},
            message='Profile retrieved successfully'
        )

    @user_ns.expect(profile_update_model)
    @user_ns.doc('update_user_profile')
    @jwt_required
    def put(self):
        """Update current user profile"""
        try:
            data = request.get_json()
            user = request.current_user

            # Update fields if provided
            if 'firstname' in data:
                user.firstname = data['firstname']
            if 'lastname' in data:
                user.lastname = data['lastname']
            if 'email' in data:
                # Check if email is already taken by another user
                existing_user = Users.query.filter_by(
                    email=data['email']).first()
                if existing_user and existing_user.userId != user.userId:
                    return error_response('Email already in use', 'EMAIL_EXISTS', 409)
                user.email = data['email']

            user.updated_at = datetime.utcnow()
            db.session.commit()

            return success_response(
                data={'user': user.to_dict()},
                message='Profile updated successfully'
            )

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Profile update error: {str(e)}")
            return error_response('Profile update failed', 'UPDATE_ERROR', 500)


@user_ns.route('/dashboard')
class UserDashboard(Resource):
    @user_ns.doc('get_user_dashboard')
    @jwt_required
    def get(self):
        """Get user dashboard data"""
        user = request.current_user

        # This would typically include quiz stats, recent activities, etc.
        dashboard_data = {
            'user': user.to_dict(),
            'stats': {
                'total_quizzes_taken': 0,  # Placeholder - implement when quiz system is ready
                'average_score': 0,        # Placeholder
                'rank': 0,                 # Placeholder
                'recent_activities': []    # Placeholder
            },
            'quick_actions': [
                {'name': 'Take Random Quiz', 'url': '/api/quiz/random'},
                {'name': 'Multi Quiz', 'url': '/api/quiz/multi'},
                {'name': 'View Leaderboard', 'url': '/api/leaderboard'}
            ]
        }

        return success_response(
            data=dashboard_data,
            message='Dashboard data retrieved successfully'
        )

# Quiz Routes (Placeholder implementations)


@quiz_ns.route('/categories')
class QuizCategories(Resource):
    @quiz_ns.doc('get_quiz_categories')
    @jwt_required
    def get(self):
        """Get available quiz categories"""
        # Placeholder implementation
        categories = [
            {'id': 1, 'name': 'General Knowledge',
                'description': 'Test your general knowledge'},
            {'id': 2, 'name': 'Science',
                'description': 'Science and technology questions'},
            {'id': 3, 'name': 'History', 'description': 'Historical events and figures'},
            {'id': 4, 'name': 'Sports', 'description': 'Sports trivia and facts'},
            {'id': 5, 'name': 'Entertainment',
                'description': 'Movies, music, and pop culture'}
        ]

        return success_response(
            data={'categories': categories},
            message='Categories retrieved successfully'
        )


@quiz_ns.route('/random')
class RandomQuiz(Resource):
    @quiz_ns.doc('get_random_quiz')
    @jwt_required
    def get(self):
        """Get a random quiz"""
        # Placeholder implementation
        quiz_data = {
            'quiz_id': 'random_001',
            'title': 'Random Knowledge Quiz',
            'description': 'Test your knowledge with random questions',
            'questions': [
                {
                    'id': 1,
                    'question': 'What is the capital of France?',
                    'options': ['London', 'Berlin', 'Paris', 'Madrid'],
                    'correct_answer': 2
                },
                {
                    'id': 2,
                    'question': 'Which planet is known as the Red Planet?',
                    'options': ['Venus', 'Mars', 'Jupiter', 'Saturn'],
                    'correct_answer': 1
                }
            ],
            'time_limit': 300,  # 5 minutes
            'total_questions': 2
        }

        return success_response(
            data=quiz_data,
            message='Random quiz retrieved successfully'
        )


@quiz_ns.route('/multi')
class MultiQuiz(Resource):
    @quiz_ns.doc('get_multi_quiz')
    @jwt_required
    def get(self):
        """Get multi-category quiz"""
        # Placeholder implementation
        quiz_data = {
            'quiz_id': 'multi_001',
            'title': 'Multi-Category Challenge',
            'description': 'Questions from multiple categories',
            'categories': ['General Knowledge', 'Science', 'History'],
            'questions': [
                {
                    'id': 1,
                    'category': 'General Knowledge',
                    'question': 'What is the largest ocean on Earth?',
                    'options': ['Atlantic', 'Pacific', 'Indian', 'Arctic'],
                    'correct_answer': 1
                },
                {
                    'id': 2,
                    'category': 'Science',
                    'question': 'What is the chemical symbol for gold?',
                    'options': ['Go', 'Gd', 'Au', 'Ag'],
                    'correct_answer': 2
                }
            ],
            'time_limit': 600,  # 10 minutes
            'total_questions': 2
        }

        return success_response(
            data=quiz_data,
            message='Multi-category quiz retrieved successfully'
        )


@user_ns.route('/quizzes')
class UserQuizzes(Resource):
    @user_ns.doc('get_user_quizzes')
    @jwt_required
    def get(self):
        """Get user's quiz history"""
        # Placeholder implementation
        user_quizzes = {
            'total_quizzes': 0,
            'quizzes': [],
            'stats': {
                'best_score': 0,
                'average_score': 0,
                'total_time_spent': 0
            }
        }

        return success_response(
            data=user_quizzes,
            message='User quizzes retrieved successfully'
        )

# Leaderboard Route


@api.route('/leaderboard')
class Leaderboard(Resource):
    @api.doc('get_leaderboard')
    @jwt_required
    def get(self):
        """Get global leaderboard"""
        # Placeholder implementation
        leaderboard_data = {
            'top_users': [
                {'rank': 1, 'username': 'QuizMaster',
                    'score': 9500, 'quizzes_completed': 95},
                {'rank': 2, 'username': 'BrainBox',
                    'score': 9200, 'quizzes_completed': 88},
                {'rank': 3, 'username': 'Smarty',
                    'score': 8900, 'quizzes_completed': 82}
            ],
            'current_user_rank': 0,  # Placeholder
            'total_participants': 1000
        }

        return success_response(
            data=leaderboard_data,
            message='Leaderboard retrieved successfully'
        )

# Error handlers


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return error_response('Resource not found', 'NOT_FOUND', 404)


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return error_response('Internal server error', 'INTERNAL_ERROR', 500)

# Middleware for automatic token refresh


@app.before_request
def before_request():
    """Middleware to handle token refresh automatically"""
    # Skip for auth endpoints and swagger
    if request.endpoint and (
        request.endpoint.startswith('auth_') or
        request.endpoint.startswith('restx_doc') or
        request.endpoint.startswith('swagger')
    ):
        return

    access_token = request.cookies.get('access_token')
    refresh_token = request.cookies.get('refresh_token')

    # If no access token but refresh token exists, try to refresh
    if not access_token and refresh_token:
        payload = verify_token(refresh_token, 'refresh')
        if payload:
            user = Users.query.get(payload['user_id'])
            if user:
                # This would typically redirect to refresh endpoint
                # For now, we'll let the individual routes handle it
                pass

# Health check endpoint


@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

# Root redirect to swagger


@app.route('/')
def root():
    """Redirect root to swagger documentation"""
    return jsonify({
        'message': 'Welcome to Quizolia API',
        'documentation': '/swagger/',
        'version': '1.0.0',
        'endpoints': {
            'authentication': '/api/auth/',
            'user': '/api/user/',
            'quiz': '/api/quiz/',
            'leaderboard': '/api/leaderboard'
        }
    })

# Initialize database
def create_tables():
    """Create database tables"""
    try:
        with app.app_context():
            db.create_all()
            app.logger.info("Database tables created successfully")
    except Exception as e:
        app.logger.error(f"Error creating database tables: {str(e)}")


if __name__ == '__main__':
    port = int(os.environ.get("PORT", 5000))
    debug = os.environ.get("FLASK_ENV") == "development"
    app.run(host="0.0.0.0", port=port, debug=debug)
