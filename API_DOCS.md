# 🚀 Quizolia API v2.0 - Complete Documentation

> **The Ultimate Guide to the Most Awesome Quiz API Ever Built** 🎯

Welcome to the comprehensive documentation for Quizolia API v2.0 - a production-ready, security-first, developer-friendly quiz application API that will make your frontend developers cry tears of joy! 😭✨

---

## 📖 Table of Contents

- [🌟 API Overview](#-api-overview)
- [🔐 Authentication System](#-authentication-system)
- [🛡️ Security Features](#️-security-features)
- [📡 API Endpoints](#-api-endpoints)
  - [🏥 Health & Utility](#-health--utility)
  - [👤 Authentication](#-authentication)
  - [👥 User Management](#-user-management)
  - [🧠 Quiz System](#-quiz-system)
- [🔄 Error Handling](#-error-handling)
- [💡 Usage Examples](#-usage-examples)
- [🧪 Testing Guide](#-testing-guide)

---

## 🌟 API Overview

### **Base Information**

- **Base URL**: `https://your-app.onrender.com` (Production) | `http://localhost:5000` (Development)
- **API Version**: `v2.0`
- **Protocol**: HTTPS (Production) | HTTP (Development)
- **Authentication**: JWT Cookie-based
- **Content-Type**: `application/json`
- **CORS**: Enabled for multiple domains

### **Key Features** ✨

- 🍪 **HTTP-Only Cookie Authentication** - XSS-proof security
- 🔄 **Automatic Token Refresh** - Seamless user experience
- 🌐 **External API Integration** - Real quiz questions from Open Trivia DB
- 🛡️ **Production-Grade Security** - CORS, input validation, error handling
- 📊 **Health Monitoring** - Built-in health checks
- 🎯 **Developer-Friendly** - Consistent responses, clear error codes

---

## 🔐 Authentication System

### **JWT Cookie-Based Authentication Flow**

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API
    participant DB as Database
    participant E as External API

    F->>A: POST /signup (user data)
    A->>DB: Create user
    A->>F: 201 Created

    F->>A: POST /login (credentials)
    A->>DB: Verify user
    A->>F: 200 OK + Set HTTP-Only Cookies
    Note over F,A: Access Token (15min) + Refresh Token (30days)

    F->>A: GET /user/profile (with cookies)
    A->>A: Verify access token
    A->>F: 200 OK + User data

    Note over F,A: When access token expires...
    F->>A: GET /user/profile (expired token)
    A->>F: 401 Unauthorized

    F->>A: POST /refresh-token (with refresh cookie)
    A->>A: Verify refresh token
    A->>F: 200 OK + New Access Token Cookie

    F->>A: Retry original request
    A->>F: 200 OK + Data
```

### **Token Specifications**

- **Access Token**: 15 minutes lifespan, used for API access
- **Refresh Token**: 30 days lifespan, used to get new access tokens
- **Storage**: HTTP-Only, Secure, SameSite=Lax cookies
- **Algorithm**: HS256 (HMAC with SHA-256)

---

## 🛡️ Security Features

| Feature                 | Implementation             | Protection Against    |
| ----------------------- | -------------------------- | --------------------- |
| **HTTP-Only Cookies**   | `httponly=True`            | XSS Attacks           |
| **Secure Cookies**      | `secure=True` (Production) | Man-in-the-middle     |
| **SameSite Protection** | `samesite='Lax'`           | CSRF Attacks          |
| **Password Hashing**    | Werkzeug PBKDF2            | Rainbow table attacks |
| **JWT Signing**         | Secret key + HS256         | Token tampering       |
| **Input Validation**    | Required field checks      | Malformed requests    |
| **CORS Configuration**  | Whitelist domains          | Unauthorized origins  |
| **SQL Injection**       | SQLAlchemy ORM             | Database attacks      |

---

## 📡 API Endpoints

### 🏥 Health & Utility

#### **GET /** - API Information

> Get basic API information and available endpoints

**Request:**

```http
GET / HTTP/1.1
Host: your-app.onrender.com
```

**Response:**

```json
{
  "message": "Welcome to Quizolia API v2.0",
  "documentation": "/swagger/",
  "version": "2.0.0",
  "endpoints": {
    "authentication": "/login, /signup, /logout",
    "user": "/user/profile",
    "quiz": "/categories, /questions",
    "refresh": "/refresh-token"
  }
}
```

**Status Codes:**

- `200 OK` - Always successful

---

#### **GET /health** - Health Check

> Monitor API and database health status

**Request:**

```http
GET /health HTTP/1.1
Host: your-app.onrender.com
```

**Response (Healthy):**

```json
{
  "status": "healthy",
  "database": "healthy",
  "timestamp": "2025-06-08T22:30:11.008540",
  "version": "2.0.0"
}
```

**Response (Degraded):**

```json
{
  "status": "degraded",
  "database": "unhealthy",
  "timestamp": "2025-06-08T22:30:11.008540",
  "version": "2.0.0"
}
```

**Status Codes:**

- `200 OK` - API is operational (check `status` field for details)

---

### 👤 Authentication

#### **POST /signup** - User Registration

> Register a new user account

**Request:**

```http
POST /signup HTTP/1.1
Host: your-app.onrender.com
Content-Type: application/json

{
  "firstname": "John",
  "lastname": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Payload Schema:**

```typescript
interface SignupRequest {
  firstname: string; // Required, 1-50 characters
  lastname: string; // Required, 1-50 characters
  email: string; // Required, valid email format
  password: string; // Required, minimum 8 characters
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:30:11.008540",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Error Responses:**

```json
// Missing required field
{
  "success": false,
  "error": "firstname is required",
  "code": "MISSING_FIELD"
}

// Email already exists
{
  "success": false,
  "error": "Email already registered",
  "code": "EMAIL_EXISTS"
}

// Database error
{
  "success": false,
  "error": "Registration failed",
  "code": "REGISTRATION_ERROR"
}
```

**Status Codes:**

- `201 Created` - User successfully registered
- `400 Bad Request` - Missing or invalid fields
- `409 Conflict` - Email already exists
- `500 Internal Server Error` - Database or server error

---

#### **POST /login** - User Authentication

> Authenticate user and set JWT cookies

**Request:**

```http
POST /login HTTP/1.1
Host: your-app.onrender.com
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Payload Schema:**

```typescript
interface LoginRequest {
  email: string; // Required, valid email
  password: string; // Required
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "Login successful",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:30:11.008540",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Response Headers:**

```http
Set-Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; Max-Age=900; HttpOnly; SameSite=Lax
Set-Cookie: refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; Max-Age=2592000; HttpOnly; SameSite=Lax
```

**Error Responses:**

```json
// Missing credentials
{
  "success": false,
  "error": "Email and password are required",
  "code": "MISSING_CREDENTIALS"
}

// Invalid credentials
{
  "success": false,
  "error": "Invalid email or password",
  "code": "INVALID_CREDENTIALS"
}

// Database connection error
{
  "success": false,
  "error": "Database connection error",
  "code": "DB_CONNECTION_ERROR"
}
```

**Status Codes:**

- `200 OK` - Login successful, cookies set
- `400 Bad Request` - Missing email or password
- `401 Unauthorized` - Invalid credentials
- `500 Internal Server Error` - Database or server error

---

#### **POST /logout** - User Logout

> Clear JWT cookies and logout user

**Request:**

```http
POST /logout HTTP/1.1
Host: your-app.onrender.com
```

**Success Response:**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

**Response Headers:**

```http
Set-Cookie: access_token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly
Set-Cookie: refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly
```

**Status Codes:**

- `200 OK` - Always successful (even if not logged in)

---

#### **POST /refresh-token** - Token Refresh

> Get a new access token using refresh token

**Request:**

```http
POST /refresh-token HTTP/1.1
Host: your-app.onrender.com
Cookie: refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Success Response:**

```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response Headers:**

```http
Set-Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; Max-Age=900; HttpOnly; SameSite=Lax
```

**Error Responses:**

```json
// Missing refresh token
{
  "success": false,
  "error": "Refresh token missing",
  "code": "REFRESH_TOKEN_MISSING"
}

// Invalid refresh token
{
  "success": false,
  "error": "Invalid or expired refresh token",
  "code": "REFRESH_TOKEN_INVALID"
}
```

**Status Codes:**

- `200 OK` - Token refreshed successfully
- `401 Unauthorized` - Missing, invalid, or expired refresh token
- `500 Internal Server Error` - Server error

---

### 👥 User Management

#### **GET /user/profile** - Get User Profile

> Retrieve current user's profile information

**Request:**

```http
GET /user/profile HTTP/1.1
Host: your-app.onrender.com
Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Success Response:**

```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:30:11.008540",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Authentication Required:** ✅ Yes

**Status Codes:**

- `200 OK` - Profile retrieved successfully
- `401 Unauthorized` - Missing, invalid, or expired token

---

#### **PUT /user/profile** - Update User Profile

> Update current user's profile information

**Request:**

```http
PUT /user/profile HTTP/1.1
Host: your-app.onrender.com
Content-Type: application/json
Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "firstname": "Jane",
  "lastname": "Smith",
  "email": "<EMAIL>"
}
```

**Payload Schema:**

```typescript
interface UpdateProfileRequest {
  firstname?: string; // Optional, 1-50 characters
  lastname?: string; // Optional, 1-50 characters
  email?: string; // Optional, valid email format
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "Jane",
      "lastname": "Smith",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:35:22.123456",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Error Responses:**

```json
// Email already in use
{
  "success": false,
  "error": "Email already in use",
  "code": "EMAIL_EXISTS"
}

// Update failed
{
  "success": false,
  "error": "Profile update failed",
  "code": "UPDATE_ERROR"
}
```

**Authentication Required:** ✅ Yes

**Status Codes:**

- `200 OK` - Profile updated successfully
- `401 Unauthorized` - Missing, invalid, or expired token
- `409 Conflict` - Email already in use by another user
- `500 Internal Server Error` - Database or server error
