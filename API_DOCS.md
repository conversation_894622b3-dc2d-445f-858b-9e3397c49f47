# 🚀 Quizolia API v2.0 - Complete Documentation

> **The Ultimate Guide to the Most Awesome Quiz API Ever Built** 🎯

Welcome to the comprehensive documentation for Quizolia API v2.0 - a production-ready, security-first, developer-friendly quiz application API that will make your frontend developers cry tears of joy! 😭✨

---

## 📖 Table of Contents

- [🌟 API Overview](#-api-overview)
- [🔐 Authentication System](#-authentication-system)
- [🛡️ Security Features](#️-security-features)
- [📡 API Endpoints](#-api-endpoints)
  - [🏥 Health & Utility](#-health--utility)
  - [👤 Authentication](#-authentication)
  - [👥 User Management](#-user-management)
  - [🧠 Quiz System](#-quiz-system)
- [🔄 Error Handling](#-error-handling)
- [💡 Usage Examples](#-usage-examples)
- [🧪 Testing Guide](#-testing-guide)

---

## 🌟 API Overview

### **Base Information**

- **Base URL**: `https://your-app.onrender.com` (Production) | `http://localhost:5000` (Development)
- **API Version**: `v2.0`
- **Protocol**: HTTPS (Production) | HTTP (Development)
- **Authentication**: JWT Cookie-based
- **Content-Type**: `application/json`
- **CORS**: Enabled for multiple domains

### **Key Features** ✨

- 🍪 **HTTP-Only Cookie Authentication** - XSS-proof security
- 🔄 **Automatic Token Refresh** - Seamless user experience
- 🌐 **External API Integration** - Real quiz questions from Open Trivia DB
- 🛡️ **Production-Grade Security** - CORS, input validation, error handling
- 📊 **Health Monitoring** - Built-in health checks
- 🎯 **Developer-Friendly** - Consistent responses, clear error codes

---

## 🔐 Authentication System

### **JWT Cookie-Based Authentication Flow**

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API
    participant DB as Database
    participant E as External API

    F->>A: POST /signup (user data)
    A->>DB: Create user
    A->>F: 201 Created

    F->>A: POST /login (credentials)
    A->>DB: Verify user
    A->>F: 200 OK + Set HTTP-Only Cookies
    Note over F,A: Access Token (15min) + Refresh Token (30days)

    F->>A: GET /user/profile (with cookies)
    A->>A: Verify access token
    A->>F: 200 OK + User data

    Note over F,A: When access token expires...
    F->>A: GET /user/profile (expired token)
    A->>F: 401 Unauthorized

    F->>A: POST /refresh-token (with refresh cookie)
    A->>A: Verify refresh token
    A->>F: 200 OK + New Access Token Cookie

    F->>A: Retry original request
    A->>F: 200 OK + Data
```

### **Token Specifications**

- **Access Token**: 15 minutes lifespan, used for API access
- **Refresh Token**: 30 days lifespan, used to get new access tokens
- **Storage**: HTTP-Only, Secure, SameSite=Lax cookies
- **Algorithm**: HS256 (HMAC with SHA-256)

---

## 🛡️ Security Features

| Feature                 | Implementation             | Protection Against    |
| ----------------------- | -------------------------- | --------------------- |
| **HTTP-Only Cookies**   | `httponly=True`            | XSS Attacks           |
| **Secure Cookies**      | `secure=True` (Production) | Man-in-the-middle     |
| **SameSite Protection** | `samesite='Lax'`           | CSRF Attacks          |
| **Password Hashing**    | Werkzeug PBKDF2            | Rainbow table attacks |
| **JWT Signing**         | Secret key + HS256         | Token tampering       |
| **Input Validation**    | Required field checks      | Malformed requests    |
| **CORS Configuration**  | Whitelist domains          | Unauthorized origins  |
| **SQL Injection**       | SQLAlchemy ORM             | Database attacks      |

---

## 📡 API Endpoints

### 🏥 Health & Utility

#### **GET /** - API Information

> Get basic API information and available endpoints

**Request:**

```http
GET / HTTP/1.1
Host: your-app.onrender.com
```

**Response:**

```json
{
  "message": "Welcome to Quizolia API v2.0",
  "documentation": "/swagger/",
  "version": "2.0.0",
  "endpoints": {
    "authentication": "/login, /signup, /logout",
    "user": "/user/profile",
    "quiz": "/categories, /questions",
    "refresh": "/refresh-token"
  }
}
```

**Status Codes:**

- `200 OK` - Always successful

---

#### **GET /health** - Health Check

> Monitor API and database health status

**Request:**

```http
GET /health HTTP/1.1
Host: your-app.onrender.com
```

**Response (Healthy):**

```json
{
  "status": "healthy",
  "database": "healthy",
  "timestamp": "2025-06-08T22:30:11.008540",
  "version": "2.0.0"
}
```

**Response (Degraded):**

```json
{
  "status": "degraded",
  "database": "unhealthy",
  "timestamp": "2025-06-08T22:30:11.008540",
  "version": "2.0.0"
}
```

**Status Codes:**

- `200 OK` - API is operational (check `status` field for details)

---

### 👤 Authentication

#### **POST /signup** - User Registration

> Register a new user account

**Request:**

```http
POST /signup HTTP/1.1
Host: your-app.onrender.com
Content-Type: application/json

{
  "firstname": "John",
  "lastname": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Payload Schema:**

```typescript
interface SignupRequest {
  firstname: string; // Required, 1-50 characters
  lastname: string; // Required, 1-50 characters
  email: string; // Required, valid email format
  password: string; // Required, minimum 8 characters
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:30:11.008540",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Error Responses:**

```json
// Missing required field
{
  "success": false,
  "error": "firstname is required",
  "code": "MISSING_FIELD"
}

// Email already exists
{
  "success": false,
  "error": "Email already registered",
  "code": "EMAIL_EXISTS"
}

// Database error
{
  "success": false,
  "error": "Registration failed",
  "code": "REGISTRATION_ERROR"
}
```

**Status Codes:**

- `201 Created` - User successfully registered
- `400 Bad Request` - Missing or invalid fields
- `409 Conflict` - Email already exists
- `500 Internal Server Error` - Database or server error

---

#### **POST /login** - User Authentication

> Authenticate user and set JWT cookies

**Request:**

```http
POST /login HTTP/1.1
Host: your-app.onrender.com
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Payload Schema:**

```typescript
interface LoginRequest {
  email: string; // Required, valid email
  password: string; // Required
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "Login successful",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:30:11.008540",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Response Headers:**

```http
Set-Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; Max-Age=900; HttpOnly; SameSite=Lax
Set-Cookie: refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; Max-Age=2592000; HttpOnly; SameSite=Lax
```

**Error Responses:**

```json
// Missing credentials
{
  "success": false,
  "error": "Email and password are required",
  "code": "MISSING_CREDENTIALS"
}

// Invalid credentials
{
  "success": false,
  "error": "Invalid email or password",
  "code": "INVALID_CREDENTIALS"
}

// Database connection error
{
  "success": false,
  "error": "Database connection error",
  "code": "DB_CONNECTION_ERROR"
}
```

**Status Codes:**

- `200 OK` - Login successful, cookies set
- `400 Bad Request` - Missing email or password
- `401 Unauthorized` - Invalid credentials
- `500 Internal Server Error` - Database or server error

---

#### **POST /logout** - User Logout

> Clear JWT cookies and logout user

**Request:**

```http
POST /logout HTTP/1.1
Host: your-app.onrender.com
```

**Success Response:**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

**Response Headers:**

```http
Set-Cookie: access_token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly
Set-Cookie: refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly
```

**Status Codes:**

- `200 OK` - Always successful (even if not logged in)

---

#### **POST /refresh-token** - Token Refresh

> Get a new access token using refresh token

**Request:**

```http
POST /refresh-token HTTP/1.1
Host: your-app.onrender.com
Cookie: refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Success Response:**

```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response Headers:**

```http
Set-Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; Max-Age=900; HttpOnly; SameSite=Lax
```

**Error Responses:**

```json
// Missing refresh token
{
  "success": false,
  "error": "Refresh token missing",
  "code": "REFRESH_TOKEN_MISSING"
}

// Invalid refresh token
{
  "success": false,
  "error": "Invalid or expired refresh token",
  "code": "REFRESH_TOKEN_INVALID"
}
```

**Status Codes:**

- `200 OK` - Token refreshed successfully
- `401 Unauthorized` - Missing, invalid, or expired refresh token
- `500 Internal Server Error` - Server error

---

### 👥 User Management

#### **GET /user/profile** - Get User Profile

> Retrieve current user's profile information

**Request:**

```http
GET /user/profile HTTP/1.1
Host: your-app.onrender.com
Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Success Response:**

```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:30:11.008540",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Authentication Required:** ✅ Yes

**Status Codes:**

- `200 OK` - Profile retrieved successfully
- `401 Unauthorized` - Missing, invalid, or expired token

---

#### **PUT /user/profile** - Update User Profile

> Update current user's profile information

**Request:**

```http
PUT /user/profile HTTP/1.1
Host: your-app.onrender.com
Content-Type: application/json
Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "firstname": "Jane",
  "lastname": "Smith",
  "email": "<EMAIL>"
}
```

**Payload Schema:**

```typescript
interface UpdateProfileRequest {
  firstname?: string; // Optional, 1-50 characters
  lastname?: string; // Optional, 1-50 characters
  email?: string; // Optional, valid email format
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "userId": 123,
      "firstname": "Jane",
      "lastname": "Smith",
      "email": "<EMAIL>",
      "created_at": "2025-06-08T22:30:11.008540",
      "updated_at": "2025-06-08T22:35:22.123456",
      "last_login": "2025-06-08T22:30:11.008540"
    }
  }
}
```

**Error Responses:**

```json
// Email already in use
{
  "success": false,
  "error": "Email already in use",
  "code": "EMAIL_EXISTS"
}

// Update failed
{
  "success": false,
  "error": "Profile update failed",
  "code": "UPDATE_ERROR"
}
```

**Authentication Required:** ✅ Yes

**Status Codes:**

- `200 OK` - Profile updated successfully
- `401 Unauthorized` - Missing, invalid, or expired token
- `409 Conflict` - Email already in use by another user
- `500 Internal Server Error` - Database or server error

---

### 🧠 Quiz System

#### **GET /categories** - Get Quiz Categories

> Retrieve available quiz categories from external API

**Request:**

```http
GET /categories HTTP/1.1
Host: your-app.onrender.com
Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Success Response:**

```json
{
  "success": true,
  "message": "Categories retrieved successfully",
  "data": {
    "categories": [
      {
        "id": 9,
        "name": "General Knowledge"
      },
      {
        "id": 10,
        "name": "Entertainment: Books"
      },
      {
        "id": 11,
        "name": "Entertainment: Film"
      },
      {
        "id": 17,
        "name": "Science & Nature"
      },
      {
        "id": 18,
        "name": "Science: Computers"
      },
      {
        "id": 19,
        "name": "Science: Mathematics"
      },
      {
        "id": 21,
        "name": "Sports"
      },
      {
        "id": 22,
        "name": "Geography"
      },
      {
        "id": 23,
        "name": "History"
      }
    ]
  }
}
```

**Fallback Response (External API Failed):**

```json
{
  "success": true,
  "message": "Categories retrieved successfully (fallback)",
  "data": {
    "categories": [
      { "id": 9, "name": "General Knowledge" },
      { "id": 10, "name": "Entertainment: Books" },
      { "id": 11, "name": "Entertainment: Film" },
      { "id": 17, "name": "Science & Nature" },
      { "id": 18, "name": "Science: Computers" }
    ]
  }
}
```

**Error Response:**

```json
{
  "success": false,
  "error": "Failed to retrieve categories",
  "code": "CATEGORIES_ERROR"
}
```

**Authentication Required:** ✅ Yes

**External API:** Open Trivia Database (`https://opentdb.com/api_category.php`)

**Status Codes:**

- `200 OK` - Categories retrieved successfully
- `401 Unauthorized` - Missing, invalid, or expired token
- `500 Internal Server Error` - External API and fallback both failed

---

#### **GET /questions** - Get Quiz Questions

> Retrieve quiz questions with customizable parameters

**Request:**

```http
GET /questions?amount=10&category=18&difficulty=medium&type=multiple HTTP/1.1
Host: your-app.onrender.com
Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**

```typescript
interface QuestionParams {
  amount?: number; // Optional, 1-50, default: 10
  category?: number; // Optional, category ID from /categories
  difficulty?: string; // Optional, "easy" | "medium" | "hard"
  type?: string; // Optional, "multiple" | "boolean"
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "Questions retrieved successfully",
  "data": {
    "questions": [
      {
        "id": 1,
        "question": "What does CPU stand for?",
        "type": "multiple",
        "difficulty": "medium",
        "category": "Science: Computers",
        "options": [
          "Central Processing Unit",
          "Computer Personal Unit",
          "Central Process Unit",
          "Central Processor Unit"
        ],
        "correct_answer": 0
      },
      {
        "id": 2,
        "question": "HTML stands for HyperText Markup Language.",
        "type": "boolean",
        "difficulty": "easy",
        "category": "Science: Computers",
        "options": ["True", "False"],
        "correct_answer": 0
      }
    ],
    "total": 2
  }
}
```

**Question Schema:**

```typescript
interface Question {
  id: number; // Sequential ID (1, 2, 3...)
  question: string; // HTML-decoded question text
  type: string; // "multiple" or "boolean"
  difficulty: string; // "easy", "medium", or "hard"
  category: string; // Category name
  options: string[]; // Shuffled answer options
  correct_answer: number; // Index of correct answer in options array
}
```

**Error Responses:**

```json
// No questions available
{
  "success": false,
  "error": "No questions available for the specified criteria",
  "code": "NO_QUESTIONS"
}

// External API error
{
  "success": false,
  "error": "Failed to fetch questions from external API",
  "code": "API_ERROR"
}

// General error
{
  "success": false,
  "error": "Failed to retrieve questions",
  "code": "QUESTIONS_ERROR"
}
```

**Authentication Required:** ✅ Yes

**External API:** Open Trivia Database (`https://opentdb.com/api.php`)

**Special Features:**

- 🔀 **Answer Shuffling**: Options are randomly shuffled for each question
- 🔤 **HTML Decoding**: All text is properly decoded from HTML entities
- 🎯 **Flexible Parameters**: Mix and match categories, difficulties, and types
- 📊 **Response Validation**: External API responses are validated and formatted

**Status Codes:**

- `200 OK` - Questions retrieved successfully
- `401 Unauthorized` - Missing, invalid, or expired token
- `404 Not Found` - No questions match the criteria
- `500 Internal Server Error` - External API error

---

#### **POST /submit-score** - Submit Quiz Score

> Submit user's quiz score for tracking (future implementation)

**Request:**

```http
POST /submit-score HTTP/1.1
Host: your-app.onrender.com
Content-Type: application/json
Cookie: access_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "score": 8,
  "total_questions": 10,
  "category": "Science: Computers",
  "difficulty": "medium",
  "time_taken": 120,
  "quiz_type": "single_category"
}
```

**Payload Schema:**

```typescript
interface SubmitScoreRequest {
  score: number; // Required, correct answers count
  total_questions: number; // Required, total questions attempted
  category: string; // Required, quiz category
  difficulty: string; // Required, quiz difficulty
  time_taken?: number; // Optional, time in seconds
  quiz_type?: string; // Optional, type of quiz
}
```

**Success Response:**

```json
{
  "success": true,
  "message": "Score submitted successfully",
  "data": {
    "user_id": 123,
    "score": 8,
    "total_questions": 10,
    "percentage": 80.0,
    "timestamp": "2025-06-08T22:45:33.123456"
  }
}
```

**Error Response:**

```json
// Missing required field
{
  "success": false,
  "error": "score is required",
  "code": "MISSING_FIELD"
}

// Submit error
{
  "success": false,
  "error": "Failed to submit score",
  "code": "SUBMIT_SCORE_ERROR"
}
```

**Authentication Required:** ✅ Yes

**Note:** 📝 This endpoint currently logs scores but doesn't persist them to database. Future implementation will include leaderboards and score history.

**Status Codes:**

- `200 OK` - Score submitted successfully
- `400 Bad Request` - Missing required fields
- `401 Unauthorized` - Missing, invalid, or expired token
- `500 Internal Server Error` - Server error

---

## 🔄 Error Handling

### **Consistent Error Response Format**

All error responses follow this standardized format:

```json
{
  "success": false,
  "error": "Human-readable error message",
  "code": "MACHINE_READABLE_ERROR_CODE"
}
```

### **Error Code Reference**

| Error Code              | Description                | HTTP Status | Common Causes                         |
| ----------------------- | -------------------------- | ----------- | ------------------------------------- |
| `TOKEN_MISSING`         | Access token not provided  | 401         | Missing Authorization header/cookie   |
| `TOKEN_INVALID`         | Invalid or expired token   | 401         | Malformed, expired, or tampered token |
| `TOKEN_EXPIRED`         | Token has expired          | 401         | Access token past expiration time     |
| `USER_NOT_FOUND`        | User does not exist        | 401         | Token valid but user deleted          |
| `MISSING_FIELD`         | Required field missing     | 400         | Incomplete request payload            |
| `MISSING_CREDENTIALS`   | Email/password missing     | 400         | Login without credentials             |
| `INVALID_CREDENTIALS`   | Wrong email/password       | 401         | Incorrect login information           |
| `EMAIL_EXISTS`          | Email already registered   | 409         | Duplicate email in signup/update      |
| `REGISTRATION_ERROR`    | User creation failed       | 500         | Database error during signup          |
| `LOGIN_ERROR`           | Authentication failed      | 500         | Database error during login           |
| `UPDATE_ERROR`          | Profile update failed      | 500         | Database error during update          |
| `DB_CONNECTION_ERROR`   | Database unavailable       | 500         | Database connection issues            |
| `DB_ERROR`              | General database error     | 500         | Database operation failed             |
| `REFRESH_TOKEN_MISSING` | Refresh token not provided | 401         | Missing refresh token cookie          |
| `REFRESH_TOKEN_INVALID` | Invalid refresh token      | 401         | Malformed or expired refresh token    |
| `REFRESH_ERROR`         | Token refresh failed       | 500         | Server error during refresh           |
| `CATEGORIES_ERROR`      | Category fetch failed      | 500         | External API and fallback failed      |
| `QUESTIONS_ERROR`       | Question fetch failed      | 500         | External API error                    |
| `NO_QUESTIONS`          | No questions available     | 404         | No questions match criteria           |
| `API_ERROR`             | External API failed        | 500         | Open Trivia DB unavailable            |
| `SUBMIT_SCORE_ERROR`    | Score submission failed    | 500         | Server error during submission        |
| `NOT_FOUND`             | Resource not found         | 404         | Invalid endpoint                      |
| `INTERNAL_ERROR`        | Server error               | 500         | Unexpected server error               |
| `UNEXPECTED_ERROR`      | Unknown error              | 500         | Unhandled exception                   |

### **Error Handling Best Practices**

#### **Frontend Error Handling**

```javascript
async function apiCall(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      credentials: "include",
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle specific error codes
      switch (data.code) {
        case "TOKEN_INVALID":
        case "TOKEN_EXPIRED":
          // Try to refresh token
          await refreshToken();
          // Retry original request
          return apiCall(url, options);

        case "TOKEN_MISSING":
          // Redirect to login
          window.location.href = "/login";
          break;

        case "EMAIL_EXISTS":
          // Show user-friendly message
          showError("This email is already registered");
          break;

        default:
          // Generic error handling
          showError(data.error || "Something went wrong");
      }

      throw new Error(data.error);
    }

    return data;
  } catch (error) {
    console.error("API Error:", error);
    throw error;
  }
}
```

#### **Automatic Token Refresh**

```javascript
async function refreshToken() {
  try {
    const response = await fetch("/refresh-token", {
      method: "POST",
      credentials: "include",
    });

    if (response.ok) {
      return true; // Token refreshed successfully
    } else {
      // Refresh failed, redirect to login
      window.location.href = "/login";
      return false;
    }
  } catch (error) {
    console.error("Token refresh failed:", error);
    window.location.href = "/login";
    return false;
  }
}
```

---

## 💡 Usage Examples

### **Complete Authentication Flow**

#### **1. User Registration**

```javascript
async function registerUser(userData) {
  try {
    const response = await fetch("/signup", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        firstname: userData.firstname,
        lastname: userData.lastname,
        email: userData.email,
        password: userData.password,
      }),
      credentials: "include",
    });

    const data = await response.json();

    if (data.success) {
      console.log("User registered:", data.data.user);
      return data.data.user;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error("Registration failed:", error);
    throw error;
  }
}

// Usage
registerUser({
  firstname: "John",
  lastname: "Doe",
  email: "<EMAIL>",
  password: "SecurePassword123!",
})
  .then((user) => {
    console.log("Registration successful!", user);
  })
  .catch((error) => {
    console.error("Registration error:", error.message);
  });
```

#### **2. User Login**

```javascript
async function loginUser(email, password) {
  try {
    const response = await fetch("/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
      credentials: "include", // Important for cookies
    });

    const data = await response.json();

    if (data.success) {
      console.log("Login successful:", data.data.user);
      // Cookies are automatically set by the browser
      return data.data.user;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error("Login failed:", error);
    throw error;
  }
}

// Usage
loginUser("<EMAIL>", "SecurePassword123!")
  .then((user) => {
    console.log("Welcome back,", user.firstname);
    // Redirect to dashboard
    window.location.href = "/dashboard";
  })
  .catch((error) => {
    console.error("Login error:", error.message);
    // Show error message to user
  });
```

#### **3. Accessing Protected Resources**

```javascript
async function getUserProfile() {
  try {
    const response = await fetch("/user/profile", {
      credentials: "include", // Sends cookies automatically
    });

    const data = await response.json();

    if (data.success) {
      return data.data.user;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error("Failed to get profile:", error);
    throw error;
  }
}

// Usage
getUserProfile()
  .then((user) => {
    console.log("User profile:", user);
    // Update UI with user data
  })
  .catch((error) => {
    console.error("Profile error:", error.message);
  });
```

### **Quiz System Integration**

#### **1. Get Quiz Categories**

```javascript
async function getQuizCategories() {
  try {
    const response = await fetch("/categories", {
      credentials: "include",
    });

    const data = await response.json();

    if (data.success) {
      return data.data.categories;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error("Failed to get categories:", error);
    throw error;
  }
}

// Usage
getQuizCategories()
  .then((categories) => {
    console.log("Available categories:", categories);
    // Populate category dropdown
    const categorySelect = document.getElementById("category-select");
    categories.forEach((cat) => {
      const option = document.createElement("option");
      option.value = cat.id;
      option.textContent = cat.name;
      categorySelect.appendChild(option);
    });
  })
  .catch((error) => {
    console.error("Categories error:", error.message);
  });
```

#### **2. Get Quiz Questions**

```javascript
async function getQuizQuestions(options = {}) {
  const params = new URLSearchParams();

  if (options.amount) params.append("amount", options.amount);
  if (options.category) params.append("category", options.category);
  if (options.difficulty) params.append("difficulty", options.difficulty);
  if (options.type) params.append("type", options.type);

  try {
    const response = await fetch(`/questions?${params.toString()}`, {
      credentials: "include",
    });

    const data = await response.json();

    if (data.success) {
      return data.data.questions;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error("Failed to get questions:", error);
    throw error;
  }
}

// Usage
getQuizQuestions({
  amount: 10,
  category: 18, // Science: Computers
  difficulty: "medium",
  type: "multiple",
})
  .then((questions) => {
    console.log("Quiz questions:", questions);
    // Start the quiz
    startQuiz(questions);
  })
  .catch((error) => {
    console.error("Questions error:", error.message);
  });
```

#### **3. Submit Quiz Score**

```javascript
async function submitQuizScore(scoreData) {
  try {
    const response = await fetch("/submit-score", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(scoreData),
      credentials: "include",
    });

    const data = await response.json();

    if (data.success) {
      return data.data;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error("Failed to submit score:", error);
    throw error;
  }
}

// Usage
submitQuizScore({
  score: 8,
  total_questions: 10,
  category: "Science: Computers",
  difficulty: "medium",
  time_taken: 120,
  quiz_type: "single_category",
})
  .then((result) => {
    console.log("Score submitted:", result);
    console.log(`You scored ${result.percentage}%!`);
    // Show results to user
  })
  .catch((error) => {
    console.error("Score submission error:", error.message);
  });
```

### **React Hook Example**

```javascript
// useQuizolia.js - Custom React Hook
import { useState, useEffect } from "react";

export function useQuizolia() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      setLoading(true);
      const response = await fetch("/user/profile", {
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.data.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
        credentials: "include",
      });

      const data = await response.json();

      if (data.success) {
        setUser(data.data.user);
        return data.data.user;
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch("/logout", {
        method: "POST",
        credentials: "include",
      });
      setUser(null);
    } catch (error) {
      console.error("Logout failed:", error);
      // Clear user anyway
      setUser(null);
    }
  };

  return {
    user,
    loading,
    error,
    login,
    logout,
    checkAuth,
    isAuthenticated: !!user,
  };
}

// Usage in component
function App() {
  const { user, loading, login, logout, isAuthenticated } = useQuizolia();

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <h1>Welcome, {user.firstname}!</h1>
          <button onClick={logout}>Logout</button>
        </div>
      ) : (
        <LoginForm onLogin={login} />
      )}
    </div>
  );
}
```

---

## 🧪 Testing Guide

### **Manual Testing with cURL**

#### **1. Health Check**

```bash
curl -X GET https://your-app.onrender.com/health
```

**Expected Response:**

```json
{
  "status": "healthy",
  "database": "healthy",
  "timestamp": "2025-06-08T22:30:11.008540",
  "version": "2.0.0"
}
```

#### **2. User Registration**

```bash
curl -X POST https://your-app.onrender.com/signup \
  -H "Content-Type: application/json" \
  -d '{
    "firstname": "Test",
    "lastname": "User",
    "email": "<EMAIL>",
    "password": "TestPassword123!"
  }'
```

#### **3. User Login**

```bash
curl -X POST https://your-app.onrender.com/login \
  -H "Content-Type: application/json" \
  -c cookies.txt \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!"
  }'
```

#### **4. Access Protected Endpoint**

```bash
curl -X GET https://your-app.onrender.com/user/profile \
  -b cookies.txt
```

#### **5. Get Quiz Categories**

```bash
curl -X GET https://your-app.onrender.com/categories \
  -b cookies.txt
```

#### **6. Get Quiz Questions**

```bash
curl -X GET "https://your-app.onrender.com/questions?amount=5&category=18&difficulty=medium" \
  -b cookies.txt
```

#### **7. Submit Score**

```bash
curl -X POST https://your-app.onrender.com/submit-score \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "score": 4,
    "total_questions": 5,
    "category": "Science: Computers",
    "difficulty": "medium"
  }'
```

#### **8. Token Refresh**

```bash
curl -X POST https://your-app.onrender.com/refresh-token \
  -b cookies.txt
```

#### **9. Logout**

```bash
curl -X POST https://your-app.onrender.com/logout \
  -b cookies.txt
```

### **Automated Testing Script**

```python
#!/usr/bin/env python3
"""
Comprehensive API Test Suite for Quizolia API v2.0
"""

import requests
import json
import sys
import time
from datetime import datetime

class QuizoliaAPITester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_user = {
            "firstname": "Test",
            "lastname": "User",
            "email": f"test_{int(time.time())}@example.com",
            "password": "TestPassword123!"
        }

    def log(self, message, status="INFO"):
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {status}: {message}")

    def test_health_check(self):
        """Test health check endpoint"""
        self.log("Testing health check...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            assert response.status_code == 200
            data = response.json()
            assert "status" in data
            assert "version" in data
            self.log("✅ Health check passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ Health check failed: {e}", "FAIL")
            return False

    def test_user_registration(self):
        """Test user registration"""
        self.log("Testing user registration...")
        try:
            response = self.session.post(
                f"{self.base_url}/signup",
                json=self.test_user
            )
            assert response.status_code == 201
            data = response.json()
            assert data["success"] == True
            assert "user" in data["data"]
            self.log("✅ User registration passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ User registration failed: {e}", "FAIL")
            return False

    def test_user_login(self):
        """Test user login"""
        self.log("Testing user login...")
        try:
            response = self.session.post(
                f"{self.base_url}/login",
                json={
                    "email": self.test_user["email"],
                    "password": self.test_user["password"]
                }
            )
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            assert "access_token" in data
            self.log("✅ User login passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ User login failed: {e}", "FAIL")
            return False

    def test_protected_endpoint(self):
        """Test protected endpoint access"""
        self.log("Testing protected endpoint...")
        try:
            response = self.session.get(f"{self.base_url}/user/profile")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            assert "user" in data["data"]
            self.log("✅ Protected endpoint passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ Protected endpoint failed: {e}", "FAIL")
            return False

    def test_quiz_categories(self):
        """Test quiz categories endpoint"""
        self.log("Testing quiz categories...")
        try:
            response = self.session.get(f"{self.base_url}/categories")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            assert "categories" in data["data"]
            assert len(data["data"]["categories"]) > 0
            self.log("✅ Quiz categories passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ Quiz categories failed: {e}", "FAIL")
            return False

    def test_quiz_questions(self):
        """Test quiz questions endpoint"""
        self.log("Testing quiz questions...")
        try:
            response = self.session.get(
                f"{self.base_url}/questions?amount=5&category=18&difficulty=medium"
            )
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            assert "questions" in data["data"]
            assert len(data["data"]["questions"]) > 0
            self.log("✅ Quiz questions passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ Quiz questions failed: {e}", "FAIL")
            return False

    def test_submit_score(self):
        """Test score submission"""
        self.log("Testing score submission...")
        try:
            response = self.session.post(
                f"{self.base_url}/submit-score",
                json={
                    "score": 4,
                    "total_questions": 5,
                    "category": "Science: Computers",
                    "difficulty": "medium"
                }
            )
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            assert "percentage" in data["data"]
            self.log("✅ Score submission passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ Score submission failed: {e}", "FAIL")
            return False

    def test_logout(self):
        """Test user logout"""
        self.log("Testing user logout...")
        try:
            response = self.session.post(f"{self.base_url}/logout")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] == True
            self.log("✅ User logout passed", "PASS")
            return True
        except Exception as e:
            self.log(f"❌ User logout failed: {e}", "FAIL")
            return False

    def run_all_tests(self):
        """Run complete test suite"""
        self.log("🚀 Starting Quizolia API Test Suite")
        self.log("=" * 50)

        tests = [
            self.test_health_check,
            self.test_user_registration,
            self.test_user_login,
            self.test_protected_endpoint,
            self.test_quiz_categories,
            self.test_quiz_questions,
            self.test_submit_score,
            self.test_logout
        ]

        passed = 0
        total = len(tests)

        for test in tests:
            if test():
                passed += 1
            time.sleep(1)  # Small delay between tests

        self.log("=" * 50)
        self.log(f"🎯 Test Results: {passed}/{total} tests passed")

        if passed == total:
            self.log("🎉 All tests passed! API is working perfectly.", "SUCCESS")
        else:
            self.log(f"⚠️ {total - passed} tests failed. Check the output above.", "WARNING")

        return passed == total

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Test Quizolia API")
    parser.add_argument("--url", default="http://localhost:5000",
                       help="API base URL (default: http://localhost:5000)")
    args = parser.parse_args()

    tester = QuizoliaAPITester(args.url)
    success = tester.run_all_tests()

    sys.exit(0 if success else 1)
```

### **Usage:**

```bash
# Test local development server
python test_api_comprehensive.py

# Test production server
python test_api_comprehensive.py --url https://your-app.onrender.com
```

---

## 🎉 Conclusion

Congratulations! You now have access to the **most comprehensive API documentation** for Quizolia v2.0. This documentation covers:

- ✅ **Complete endpoint reference** with request/response examples
- ✅ **Authentication flow** with JWT cookie implementation
- ✅ **Error handling** with detailed error codes
- ✅ **Usage examples** in JavaScript and React
- ✅ **Testing guide** with cURL and Python scripts
- ✅ **Security features** and best practices

### **Quick Reference Card** 🃏

| Endpoint         | Method  | Auth Required | Purpose             |
| ---------------- | ------- | ------------- | ------------------- |
| `/health`        | GET     | ❌            | Health check        |
| `/`              | GET     | ❌            | API information     |
| `/signup`        | POST    | ❌            | User registration   |
| `/login`         | POST    | ❌            | User authentication |
| `/logout`        | POST    | ❌            | User logout         |
| `/refresh-token` | POST    | 🍪            | Token refresh       |
| `/user/profile`  | GET/PUT | ✅            | User profile        |
| `/categories`    | GET     | ✅            | Quiz categories     |
| `/questions`     | GET     | ✅            | Quiz questions      |
| `/submit-score`  | POST    | ✅            | Submit score        |

### **Remember:**

- 🍪 Always include `credentials: 'include'` in fetch requests
- 🔄 Implement automatic token refresh for seamless UX
- 🛡️ Handle errors gracefully with specific error codes
- 🧪 Test thoroughly before deploying to production

**Happy coding! 🚀 Build something amazing with Quizolia API!**
