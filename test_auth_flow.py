#!/usr/bin/env python3
"""
Test script to verify the authentication flow between frontend and backend
"""

import requests
import json
import sys

def test_auth_flow(base_url="http://localhost:5000"):
    """Test the complete authentication flow"""
    
    print(f"🧪 Testing authentication flow with {base_url}")
    print("=" * 60)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Test user data
    test_user = {
        "firstname": "Test",
        "lastname": "User",
        "email": f"test_auth_{int(__import__('time').time())}@example.com",
        "password": "TestPassword123!"
    }
    
    try:
        # Step 1: Test health check
        print("1️⃣ Testing health check...")
        response = session.get(f"{base_url}/health")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Health check passed")
        else:
            print("   ❌ Health check failed")
            return False
        
        # Step 2: Test signup
        print("\n2️⃣ Testing user signup...")
        response = session.post(f"{base_url}/signup", json=test_user)
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            print("   ✅ Signup successful")
        else:
            print(f"   ❌ Signup failed: {response.text}")
            return False
        
        # Step 3: Test login
        print("\n3️⃣ Testing user login...")
        login_data = {
            "email": test_user["email"],
            "password": test_user["password"]
        }
        response = session.post(f"{base_url}/login", json=login_data)
        print(f"   Status: {response.status_code}")
        print(f"   Cookies set: {list(session.cookies.keys())}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Login successful")
            print(f"   User: {data['data']['user']['firstname']} {data['data']['user']['lastname']}")
        else:
            print(f"   ❌ Login failed: {response.text}")
            return False
        
        # Step 4: Test debug endpoint
        print("\n4️⃣ Testing debug endpoint...")
        response = session.get(f"{base_url}/debug/auth")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            debug_data = response.json()
            print("   ✅ Debug endpoint accessible")
            print(f"   Cookies received: {debug_data['data']['cookies_received']}")
            print(f"   Environment: {debug_data['data']['environment']}")
        else:
            print(f"   ❌ Debug endpoint failed: {response.text}")
        
        # Step 5: Test protected endpoint (categories)
        print("\n5️⃣ Testing protected endpoint (/categories)...")
        response = session.get(f"{base_url}/categories")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Categories endpoint accessible")
            print(f"   Categories found: {len(data['data']['categories'])}")
        elif response.status_code == 401:
            print("   ❌ Categories endpoint returned 401 - Authentication failed!")
            error_data = response.json()
            print(f"   Error: {error_data.get('error')}")
            print(f"   Code: {error_data.get('code')}")
            if 'debug' in error_data:
                print(f"   Debug info: {error_data['debug']}")
            return False
        else:
            print(f"   ❌ Categories endpoint failed: {response.text}")
            return False
        
        # Step 6: Test token refresh
        print("\n6️⃣ Testing token refresh...")
        response = session.post(f"{base_url}/refresh-token")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Token refresh successful")
        else:
            print(f"   ❌ Token refresh failed: {response.text}")
        
        # Step 7: Test logout
        print("\n7️⃣ Testing logout...")
        response = session.post(f"{base_url}/logout")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Logout successful")
        else:
            print(f"   ❌ Logout failed: {response.text}")
        
        # Step 8: Verify logout worked
        print("\n8️⃣ Verifying logout (should get 401)...")
        response = session.get(f"{base_url}/categories")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 401:
            print("   ✅ Logout verified - protected endpoint now returns 401")
        else:
            print(f"   ❌ Logout verification failed - still getting: {response.status_code}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 All authentication tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {str(e)}")
        return False

def test_cors_headers(base_url="http://localhost:5000"):
    """Test CORS headers"""
    print(f"\n🌐 Testing CORS headers with {base_url}")
    print("=" * 60)
    
    # Test preflight request
    headers = {
        'Origin': 'https://quizolia-frontend.vercel.app',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
    }
    
    try:
        response = requests.options(f"{base_url}/categories", headers=headers)
        print(f"Preflight Status: {response.status_code}")
        print("CORS Headers:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"  {header}: {value}")
        
        if 'Access-Control-Allow-Credentials' in response.headers:
            print("✅ CORS credentials allowed")
        else:
            print("❌ CORS credentials not allowed")
            
    except Exception as e:
        print(f"❌ CORS test failed: {str(e)}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Quizolia authentication flow")
    parser.add_argument("--url", default="http://localhost:5000", 
                       help="API base URL (default: http://localhost:5000)")
    parser.add_argument("--cors", action="store_true", 
                       help="Also test CORS headers")
    args = parser.parse_args()
    
    success = test_auth_flow(args.url)
    
    if args.cors:
        test_cors_headers(args.url)
    
    if success:
        print("\n🎯 Authentication flow is working correctly!")
        print("The backend should work with your frontend now.")
    else:
        print("\n⚠️ Authentication flow has issues.")
        print("Check the error messages above for debugging.")
    
    sys.exit(0 if success else 1)
