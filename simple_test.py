#!/usr/bin/env python3
"""
Simple test to verify the API components work
"""

from flask import Flask, jsonify
from flask_restx import Api
import os

print("Starting simple test...")

# Create a simple Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'

# Initialize Flask-RESTX
api = Api(app, 
    version='1.0', 
    title='Test API',
    description='A simple test API',
    doc='/swagger/'
)

@app.route('/health')
def health():
    return jsonify({'status': 'healthy'})

@app.route('/')
def root():
    return jsonify({'message': 'Test API is working'})

if __name__ == '__main__':
    print("Starting test server on port 5000...")
    app.run(host='0.0.0.0', port=5000, debug=True)
