# Quizolia API - Deployment Guide

## Issues Fixed ✅

### 1. Database Connection Issues

- **Problem**: MySQL connection errors and "Lost connection to server during query"
- **Solution**:
  - Added PyMySQL driver with proper configuration
  - Implemented connection pooling and retry logic
  - Added timeout settings for better reliability

### 2. Route 404 Errors

- **Problem**: Routes like `/api/auth/user/profile` returning 404
- **Solution**:
  - Fixed Flask-RESTX namespace registration
  - Added backwards compatibility routes
  - Corrected route path configurations

### 3. Database Configuration

- **Problem**: SQLAlchemy engine options not applied correctly
- **Solution**: Moved configuration before SQLAlchemy initialization

## Deployment Instructions for Render

### 1. Environment Variables

Set these in your Render dashboard:

```bash
SQLALCHEMY_DATABASE_URI=mysql+pymysql://username:password@hostname:port/database_name
SECRET_KEY=your-very-secure-secret-key-here
FLASK_ENV=production
PORT=10000
```

### 2. Start Command

In Render, set your start command to:

```bash
python start.py
```

### 3. Requirements

The `requirements.txt` has been updated with:

- PyMySQL (instead of mysql)
- All necessary Flask extensions

### 4. Database Connection

The app now handles MySQL connections properly with:

- Connection pooling
- Automatic reconnection
- Retry logic for failed queries
- Proper timeout settings

## API Endpoints

### Authentication

- `POST /api/auth/signup` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/verify` - Token verification

### User Management (New)

- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `GET /api/user/dashboard` - Get dashboard data
- `GET /api/user/quizzes` - Get user quiz history

### Backwards Compatibility

- `GET /api/auth/user/profile` - Profile (legacy route)
- `GET /api/auth/user/dashboard` - Dashboard (legacy route)
- `GET /api/auth/categories` - Categories (legacy route)

### Quiz

- `GET /api/quiz/categories` - Get quiz categories
- `GET /api/quiz/random` - Get random quiz
- `GET /api/quiz/multi` - Get multi-category quiz

### Other

- `GET /health` - Health check
- `GET /` - API information
- `GET /swagger/` - API documentation

## Testing

Run the test script:

```bash
python test_simple.py
```

Or test with your deployed URL:

```bash
python test_simple.py https://your-app.onrender.com
```

## Common Issues & Solutions

### Database Connection Lost

- The app now automatically retries failed database connections
- Check your MySQL server is accessible from Render
- Verify your connection string format: `mysql+pymysql://...`

### JWT Authentication

- Tokens are stored in HTTP-only cookies
- 15-minute access token lifetime
- 30-day refresh token lifetime

### CORS

Configured for these origins:

- `http://localhost:3000`
- `https://quizolia.vercel.app`
- `https://quizolia-bolt.vercel.app`
- `https://quizolia.netlify.app`

Add your production frontend URL to the CORS origins in `app.py` if needed.
