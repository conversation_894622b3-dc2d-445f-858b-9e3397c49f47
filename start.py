#!/usr/bin/env python3
"""
Production startup script for Quizolia API on Render
"""

import os
import sys
from app import app, create_tables


def main():
    """Main function to start the application"""
    # Ensure we're in the correct directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    # Set production environment
    os.environ['FLASK_ENV'] = 'production'

    # Create database tables
    print("🔄 Creating database tables...")
    try:
        create_tables()
        print("✅ Database tables created/verified")
    except Exception as e:
        print(f"⚠️  Database table creation warning: {e}")
        # Don't exit - the app might still work if tables already exist

    # Get port from environment (<PERSON>der sets this)
    port = int(os.environ.get("PORT", 5000))

    print(f"🚀 Starting Quizolia API on port {port}")
    print(f"📊 Environment: {os.environ.get('FLASK_ENV', 'development')}")

    # Start the application
    app.run(
        host="0.0.0.0",
        port=port,
        debug=False,  # Always False in production
        threaded=True
    )


if __name__ == "__main__":
    main()
