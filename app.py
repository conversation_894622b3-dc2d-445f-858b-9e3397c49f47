"""
Quizolia API - Main Application File
A comprehensive quiz application API with JWT authentication and Swagger documentation.
"""

from flask import Flask, request, jsonify, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_restx import Api, Resource, fields, Namespace
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import jwt
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta
import pymysql

# Load environment variables
load_dotenv()

# Install PyMySQL as MySQLdb compatibility layer
pymysql.install_as_MySQLdb()

# Initialize Flask app
app = Flask(__name__)

# Database configuration with fallback
database_url = os.getenv('SQLALCHEMY_DATABASE_URI')
if database_url and 'mysql://' in database_url:
    # Convert mysql:// to mysql+pymysql:// for compatibility
    database_url = database_url.replace('mysql://', 'mysql+pymysql://')

app.config['SQLALCHEMY_DATABASE_URI'] = database_url
app.config['SECRET_KEY'] = os.getenv(
    'SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(minutes=15)
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Add database connection pooling and timeout settings for better reliability
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
    'pool_timeout': 20,
    'pool_size': 10,
    'max_overflow': 20,
    'connect_args': {
        "charset": "utf8mb4",
        "autocommit": True,
        "connect_timeout": 10,
        "read_timeout": 10,
        "write_timeout": 10
    }
}

# Initialize extensions
db = SQLAlchemy(app)

CORS(app, supports_credentials=True, origins=[
    'http://localhost:3000', 'http://127.0.0.1:3000', 'https://quizolia.vercel.app', 'https://quizolia-bolt.vercel.app', 'https://quizolia.netlify.app'])

# Initialize Flask-RESTX for Swagger
api = Api(app,
          version='1.0',
          title='Quizolia API',
          description='A comprehensive quiz application API with JWT authentication',
          doc='/swagger/'
          )

# User model


class Users(db.Model):
    """User database model"""
    __tablename__ = 'users'
    userId = db.Column(db.Integer, primary_key=True, autoincrement=True)
    firstname = db.Column(db.String(50), nullable=False)
    lastname = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(100), nullable=False, unique=True)
    password = db.Column(db.String(255), nullable=False)
    created_at = db.Column(
        db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow,
                           onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)

    def to_dict(self):
        """Convert user object to dictionary"""
        return {
            'userId': self.userId,
            'firstname': self.firstname,
            'lastname': self.lastname,
            'email': self.email,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

# JWT Utility Functions


def generate_access_token(user_id):
    """Generate JWT access token"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + app.config['JWT_ACCESS_TOKEN_EXPIRES'],
        'iat': datetime.utcnow(),
        'type': 'access'
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')


def generate_refresh_token(user_id):
    """Generate JWT refresh token"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + app.config['JWT_REFRESH_TOKEN_EXPIRES'],
        'iat': datetime.utcnow(),
        'type': 'refresh'
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')


def verify_token(token, token_type='access'):
    """Verify JWT token"""
    try:
        payload = jwt.decode(
            token, app.config['SECRET_KEY'], algorithms=['HS256'])
        if payload.get('type') != token_type:
            return None
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

# Authentication decorator


def jwt_required(f):
    """Decorator to require JWT authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        access_token = request.cookies.get('access_token')

        if not access_token:
            return jsonify({'error': 'Access token missing', 'code': 'TOKEN_MISSING'}), 401

        payload = verify_token(access_token, 'access')
        if not payload:
            return jsonify({'error': 'Invalid or expired token', 'code': 'TOKEN_INVALID'}), 401

        user = Users.query.get(payload['user_id'])
        if not user:
            return jsonify({'error': 'User not found', 'code': 'USER_NOT_FOUND'}), 401

        request.current_user = user
        return f(*args, **kwargs)

    return decorated_function

# Authentication Routes (using regular Flask routes for cookie handling)


@app.route('/api/auth/signup', methods=['POST'])
def signup():
    """Register a new user"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['firstname', 'lastname', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required',
                    'code': 'MISSING_FIELD'
                }), 400

        # Check if user already exists with retry logic
        try:
            existing_user = Users.query.filter_by(email=data['email']).first()
        except Exception as db_error:
            app.logger.error(
                f"Database error during signup check: {str(db_error)}")
            try:
                db.session.rollback()
                existing_user = Users.query.filter_by(
                    email=data['email']).first()
            except Exception as retry_error:
                app.logger.error(
                    f"Database retry failed during signup: {str(retry_error)}")
                return jsonify({
                    'success': False,
                    'error': 'Database connection error. Please try again.',
                    'code': 'DATABASE_ERROR'
                }), 503

        if existing_user:
            return jsonify({
                'success': False,
                'error': 'Email already registered',
                'code': 'EMAIL_EXISTS'
            }), 409

        # Create new user
        hashed_password = generate_password_hash(data['password'])
        new_user = Users(
            firstname=data['firstname'],
            lastname=data['lastname'],
            email=data['email'],
            password=hashed_password,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )

        db.session.add(new_user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'data': {'user': new_user.to_dict()}
        }), 201

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Signup error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Registration failed',
            'code': 'REGISTRATION_ERROR'
        }), 500


@app.route('/api/auth/login', methods=['POST'])
def login():
    """Authenticate user and set JWT cookies"""
    try:
        data = request.get_json()

        if not data.get('email') or not data.get('password'):
            return jsonify({
                'success': False,
                'error': 'Email and password are required',
                'code': 'MISSING_CREDENTIALS'
            }), 400

        # Find user with retry logic
        try:
            user = Users.query.filter_by(email=data['email']).first()
        except Exception as db_error:
            app.logger.error(f"Database error during login: {str(db_error)}")
            # Try to reconnect and retry once
            try:
                db.session.rollback()
                user = Users.query.filter_by(email=data['email']).first()
            except Exception as retry_error:
                app.logger.error(f"Database retry failed: {str(retry_error)}")
                return jsonify({
                    'success': False,
                    'error': 'Database connection error. Please try again.',
                    'code': 'DATABASE_ERROR'
                }), 503

        if not user or not check_password_hash(user.password, data['password']):
            return jsonify({
                'success': False,
                'error': 'Invalid email or password',
                'code': 'INVALID_CREDENTIALS'
            }), 401

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        # Generate tokens
        access_token = generate_access_token(user.userId)
        refresh_token = generate_refresh_token(user.userId)

        # Create response
        response = make_response(jsonify({
            'success': True,
            'message': 'Login successful',
            'data': {'user': user.to_dict()}
        }))

        # Set HTTP-only cookies
        response.set_cookie(
            'access_token',
            access_token,
            max_age=int(
                app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds()),
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite='Lax'
        )

        response.set_cookie(
            'refresh_token',
            refresh_token,
            max_age=int(
                app.config['JWT_REFRESH_TOKEN_EXPIRES'].total_seconds()),
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite='Lax'
        )

        return response

    except Exception as e:
        app.logger.error(f"Login error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Login failed',
            'code': 'LOGIN_ERROR'
        }), 500


@app.route('/api/auth/logout', methods=['POST'])
def logout():
    """Logout user and clear JWT cookies"""
    response = make_response(jsonify({
        'success': True,
        'message': 'Logged out successfully'
    }))

    # Clear cookies
    response.set_cookie('access_token', '', expires=0, httponly=True)
    response.set_cookie('refresh_token', '', expires=0, httponly=True)

    return response


@app.route('/api/auth/verify', methods=['GET'])
@jwt_required
def verify_token_endpoint():
    """Verify current access token and return user info"""
    return jsonify({
        'success': True,
        'message': 'Token is valid',
        'data': {'user': request.current_user.to_dict()}
    })


# Auth redirection routes for backwards compatibility
@app.route('/api/auth/user/profile', methods=['GET'])
@jwt_required
def auth_user_profile():
    """Get user profile (backwards compatibility)"""
    return jsonify({
        'success': True,
        'message': 'Profile retrieved successfully',
        'data': {'user': request.current_user.to_dict()}
    })


@app.route('/api/auth/user/dashboard', methods=['GET'])
@jwt_required
def auth_user_dashboard():
    """Get user dashboard (backwards compatibility)"""
    user = request.current_user

    dashboard_data = {
        'user': user.to_dict(),
        'stats': {
            'total_quizzes_taken': 0,
            'average_score': 0,
            'rank': 0,
            'recent_activities': []
        },
        'quick_actions': [
            {'name': 'Take Random Quiz', 'url': '/api/quiz/random'},
            {'name': 'Multi Quiz', 'url': '/api/quiz/multi'},
            {'name': 'View Leaderboard', 'url': '/api/auth/user/leaderboard'}
        ]
    }

    return jsonify({
        'success': True,
        'message': 'Dashboard data retrieved successfully',
        'data': dashboard_data
    })


@app.route('/api/auth/categories', methods=['GET'])
@jwt_required
def auth_categories():
    """Get quiz categories (backwards compatibility)"""
    categories = [
        {'id': 1, 'name': 'General Knowledge',
            'description': 'Test your general knowledge'},
        {'id': 2, 'name': 'Science', 'description': 'Science and technology questions'},
        {'id': 3, 'name': 'History', 'description': 'Historical events and figures'},
        {'id': 4, 'name': 'Sports', 'description': 'Sports trivia and facts'},
        {'id': 5, 'name': 'Entertainment',
            'description': 'Movies, music, and pop culture'}
    ]

    return jsonify({
        'success': True,
        'message': 'Categories retrieved successfully',
        'data': {'categories': categories}
    })


# Create namespaces for Swagger documentation
user_ns = Namespace('user', description='User operations', path='/api/user')
quiz_ns = Namespace('quiz', description='Quiz operations', path='/api/quiz')

api.add_namespace(user_ns)
api.add_namespace(quiz_ns)

# API Models for Swagger documentation
user_model = api.model('User', {
    'userId': fields.Integer(description='User ID'),
    'firstname': fields.String(description='First name'),
    'lastname': fields.String(description='Last name'),
    'email': fields.String(description='Email address'),
    'created_at': fields.String(description='Creation timestamp'),
    'updated_at': fields.String(description='Last update timestamp'),
    'last_login': fields.String(description='Last login timestamp')
})

profile_update_model = api.model('ProfileUpdate', {
    'firstname': fields.String(description='First name'),
    'lastname': fields.String(description='Last name'),
    'email': fields.String(description='Email address')
})

# User Routes


@user_ns.route('/profile')
class UserProfile(Resource):
    @user_ns.doc('get_user_profile')
    @jwt_required
    def get(self):
        """Get current user profile"""
        return {
            'success': True,
            'message': 'Profile retrieved successfully',
            'data': {'user': request.current_user.to_dict()}
        }

    @user_ns.expect(profile_update_model)
    @user_ns.doc('update_user_profile')
    @jwt_required
    def put(self):
        """Update current user profile"""
        try:
            data = request.get_json()
            user = request.current_user

            # Update fields if provided
            if 'firstname' in data:
                user.firstname = data['firstname']
            if 'lastname' in data:
                user.lastname = data['lastname']
            if 'email' in data:
                # Check if email is already taken by another user
                existing_user = Users.query.filter_by(
                    email=data['email']).first()
                if existing_user and existing_user.userId != user.userId:
                    return {
                        'success': False,
                        'error': 'Email already in use',
                        'code': 'EMAIL_EXISTS'
                    }, 409
                user.email = data['email']

            user.updated_at = datetime.utcnow()
            db.session.commit()

            return {
                'success': True,
                'message': 'Profile updated successfully',
                'data': {'user': user.to_dict()}
            }

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Profile update error: {str(e)}")
            return {
                'success': False,
                'error': 'Profile update failed',
                'code': 'UPDATE_ERROR'
            }, 500


@user_ns.route('/dashboard')
class UserDashboard(Resource):
    @user_ns.doc('get_user_dashboard')
    @jwt_required
    def get(self):
        """Get user dashboard data"""
        user = request.current_user

        dashboard_data = {
            'user': user.to_dict(),
            'stats': {
                'total_quizzes_taken': 0,
                'average_score': 0,
                'rank': 0,
                'recent_activities': []
            },
            'quick_actions': [
                {'name': 'Take Random Quiz', 'url': '/api/quiz/random'},
                {'name': 'Multi Quiz', 'url': '/api/quiz/multi'},
                {'name': 'View Leaderboard', 'url': '/api/leaderboard'}
            ]
        }

        return {
            'success': True,
            'message': 'Dashboard data retrieved successfully',
            'data': dashboard_data
        }


@user_ns.route('/quizzes')
class UserQuizzes(Resource):
    @user_ns.doc('get_user_quizzes')
    @jwt_required
    def get(self):
        """Get user's quiz history"""
        user_quizzes = {
            'total_quizzes': 0,
            'quizzes': [],
            'stats': {
                'best_score': 0,
                'average_score': 0,
                'total_time_spent': 0
            }
        }

        return {
            'success': True,
            'message': 'User quizzes retrieved successfully',
            'data': user_quizzes
        }

# Quiz Routes


@quiz_ns.route('/categories')
class QuizCategories(Resource):
    @quiz_ns.doc('get_quiz_categories')
    @jwt_required
    def get(self):
        """Get available quiz categories"""
        categories = [
            {'id': 1, 'name': 'General Knowledge',
                'description': 'Test your general knowledge'},
            {'id': 2, 'name': 'Science',
                'description': 'Science and technology questions'},
            {'id': 3, 'name': 'History', 'description': 'Historical events and figures'},
            {'id': 4, 'name': 'Sports', 'description': 'Sports trivia and facts'},
            {'id': 5, 'name': 'Entertainment',
                'description': 'Movies, music, and pop culture'}
        ]

        return {
            'success': True,
            'message': 'Categories retrieved successfully',
            'data': {'categories': categories}
        }


@quiz_ns.route('/random')
class RandomQuiz(Resource):
    @quiz_ns.doc('get_random_quiz')
    @jwt_required
    def get(self):
        """Get a random quiz"""
        quiz_data = {
            'quiz_id': 'random_001',
            'title': 'Random Knowledge Quiz',
            'description': 'Test your knowledge with random questions',
            'questions': [
                {
                    'id': 1,
                    'question': 'What is the capital of France?',
                    'options': ['London', 'Berlin', 'Paris', 'Madrid'],
                    'correct_answer': 2
                },
                {
                    'id': 2,
                    'question': 'Which planet is known as the Red Planet?',
                    'options': ['Venus', 'Mars', 'Jupiter', 'Saturn'],
                    'correct_answer': 1
                }
            ],
            'time_limit': 300,
            'total_questions': 2
        }

        return {
            'success': True,
            'message': 'Random quiz retrieved successfully',
            'data': quiz_data
        }


@quiz_ns.route('/multi')
class MultiQuiz(Resource):
    @quiz_ns.doc('get_multi_quiz')
    @jwt_required
    def get(self):
        """Get multi-category quiz"""
        quiz_data = {
            'quiz_id': 'multi_001',
            'title': 'Multi-Category Challenge',
            'description': 'Questions from multiple categories',
            'categories': ['General Knowledge', 'Science', 'History'],
            'questions': [
                {
                    'id': 1,
                    'category': 'General Knowledge',
                    'question': 'What is the largest ocean on Earth?',
                    'options': ['Atlantic', 'Pacific', 'Indian', 'Arctic'],
                    'correct_answer': 1
                },
                {
                    'id': 2,
                    'category': 'Science',
                    'question': 'What is the chemical symbol for gold?',
                    'options': ['Go', 'Gd', 'Au', 'Ag'],
                    'correct_answer': 2
                }
            ],
            'time_limit': 600,
            'total_questions': 2
        }

        return {
            'success': True,
            'message': 'Multi-category quiz retrieved successfully',
            'data': quiz_data
        }

# Leaderboard Route


@app.route('/api/auth/user/leaderboard', methods=['GET'])
@jwt_required
def leaderboard():
    """Get global leaderboard"""
    leaderboard_data = {
        'top_users': [
            {'rank': 1, 'username': 'QuizMaster',
                'score': 9500, 'quizzes_completed': 95},
            {'rank': 2, 'username': 'BrainBox',
                'score': 9200, 'quizzes_completed': 88},
            {'rank': 3, 'username': 'Smarty', 'score': 8900, 'quizzes_completed': 82}
        ],
        'current_user_rank': 0,
        'total_participants': 1000
    }

    return jsonify({
        'success': True,
        'message': 'Leaderboard retrieved successfully',
        'data': leaderboard_data
    })

# Health check endpoint


@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

# API info route


@app.route('/')
def api_info():
    """API information endpoint"""
    return jsonify({
        'message': 'Welcome to Quizolia API',
        'documentation': '/swagger/',
        'version': '1.0.0',
        'endpoints': {
            'authentication': '/api/auth/',
            'user': '/api/user/',
            'quiz': '/api/quiz/',
            'leaderboard': '/api/leaderboard'
        }
    })

# Initialize database


def create_tables():
    """Create database tables"""
    try:
        with app.app_context():
            db.create_all()
            app.logger.info("Database tables created successfully")
    except Exception as e:
        app.logger.error(f"Error creating database tables: {str(e)}")


if __name__ == '__main__':
    # Create tables on startup
    create_tables()

    port = int(os.environ.get("PORT", 5000))
    debug = os.environ.get("FLASK_ENV") == "development"
    app.run(host="0.0.0.0", port=port, debug=debug)
