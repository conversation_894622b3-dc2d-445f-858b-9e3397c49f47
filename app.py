"""
Quizolia API - Production Backend
A comprehensive quiz application API with JWT authentication, external quiz API integration,
and proper error handling for production deployment.
"""

from dotenv import load_dotenv
from flask import Flask, request, jsonify, make_response
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import jwt
import os
import requests
import logging
from datetime import datetime, timedelta
import pymysql

# Install PyMySQL as MySQLdb compatibility layer
pymysql.install_as_MySQLdb()

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Logging configuration
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration with connection pooling
database_url = os.getenv('SQLALCHEMY_DATABASE_URI')
if database_url and 'mysql://' in database_url:
    database_url = database_url.replace('mysql://', 'mysql+pymysql://')

app.config.update({
    'SQLALCHEMY_DATABASE_URI': database_url,
    'SECRET_KEY': os.getenv('SECRET_KEY'),
    'JWT_ACCESS_TOKEN_EXPIRES': timedelta(minutes=15),
    'JWT_REFRESH_TOKEN_EXPIRES': timedelta(days=30),
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SQLALCHEMY_ENGINE_OPTIONS': {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0,
        'connect_args': {
            'connect_timeout': 60,
            'read_timeout': 60,
            'write_timeout': 60
        }
    }
})

# Initialize extensions
db = SQLAlchemy(app)

# CORS configuration for production with proper cookie support
CORS(app,
     supports_credentials=True,
     origins=[
         'http://localhost:3000',
         'http://localhost:5173',  # Vite default port
         'http://localhost:8080',
         'http://localhost:8081',
         'http://127.0.0.1:3000',
         'http://127.0.0.1:5173',
         'http://127.0.0.1:8080',
         'http://127.0.0.1:8081',
         'https://quizolia.vercel.app',
         'https://quizolia.netlify.app',
         'https://quizolia-bolt.vercel.app',
         'https://quizolia-frontend.vercel.app',
         # Add all possible Vercel deployment URLs
         'https://quizolia-frontend-git-main-dohoudaniels-projects.vercel.app',
         'https://quizolia-frontend-dohoudaniels-projects.vercel.app',
         # Add wildcard support for Vercel preview deployments
         'https://quizolia-frontend-*.vercel.app'
     ],
     allow_headers=[
         'Content-Type',
         'Authorization',
         'X-Requested-With',
         'Accept',
         'Origin',
         'Access-Control-Request-Method',
         'Access-Control-Request-Headers'
     ],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     expose_headers=['Set-Cookie', 'Access-Control-Allow-Credentials']
     )

# User model


class Users(db.Model):
    """User database model"""
    __tablename__ = 'users'
    userId = db.Column(db.Integer, primary_key=True, autoincrement=True)
    firstname = db.Column(db.String(50), nullable=False)
    lastname = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(100), nullable=False, unique=True)
    password = db.Column(db.String(255), nullable=False)
    created_at = db.Column(
        db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow,
                           onupdate=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)

    def to_dict(self):
        """Convert user object to dictionary"""
        return {
            'userId': self.userId,
            'firstname': self.firstname,
            'lastname': self.lastname,
            'email': self.email,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

# JWT Utility Functions


def generate_access_token(user_id):
    """Generate JWT access token"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + app.config['JWT_ACCESS_TOKEN_EXPIRES'],
        'iat': datetime.utcnow(),
        'type': 'access'
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')


def generate_refresh_token(user_id):
    """Generate JWT refresh token"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + app.config['JWT_REFRESH_TOKEN_EXPIRES'],
        'iat': datetime.utcnow(),
        'type': 'refresh'
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')


def verify_token(token, token_type='access'):
    """Verify JWT token"""
    try:
        payload = jwt.decode(
            token, app.config['SECRET_KEY'], algorithms=['HS256'])
        if payload.get('type') != token_type:
            return None
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning(f"Token expired: {token_type}")
        return None
    except jwt.InvalidTokenError:
        logger.warning(f"Invalid token: {token_type}")
        return None

# Authentication decorator


def jwt_required(f):
    """Decorator to require JWT authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        access_token = request.cookies.get('access_token')

        if not access_token:
            logger.warning(
                f"Access token missing for {request.endpoint}. Origin: {request.headers.get('Origin')}")
            return jsonify({
                'error': 'Access token missing',
                'code': 'TOKEN_MISSING',
                'debug': {
                    'cookies_received': list(request.cookies.keys()),
                    'origin': request.headers.get('Origin'),
                    'endpoint': request.endpoint
                }
            }), 401

        payload = verify_token(access_token, 'access')
        if not payload:
            logger.warning(
                f"Invalid token for {request.endpoint}. Origin: {request.headers.get('Origin')}")
            return jsonify({
                'error': 'Invalid or expired token',
                'code': 'TOKEN_INVALID',
                'debug': {
                    'origin': request.headers.get('Origin'),
                    'endpoint': request.endpoint
                }
            }), 401

        try:
            user = Users.query.filter_by(userId=payload['user_id']).first()
            if not user:
                return jsonify({'error': 'User not found', 'code': 'USER_NOT_FOUND'}), 401

            request.current_user = user
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Database error in auth: {str(e)}")
            return jsonify({'error': 'Database error', 'code': 'DB_ERROR'}), 500

    return decorated_function

# Error handlers


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({'error': 'Resource not found', 'code': 'NOT_FOUND'}), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({'error': 'Internal server error', 'code': 'INTERNAL_ERROR'}), 500


@app.errorhandler(Exception)
def handle_exception(e):
    """Handle all exceptions"""
    logger.error(f"Unhandled exception: {str(e)}")
    return jsonify({'error': 'An unexpected error occurred', 'code': 'UNEXPECTED_ERROR'}), 500

# Health check endpoint


@app.route('/health')
def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        db_status = 'unhealthy'

    return jsonify({
        'status': 'healthy' if db_status == 'healthy' else 'degraded',
        'database': db_status,
        'timestamp': datetime.utcnow().isoformat(),
        'version': '2.0.0'
    })

# Root endpoint


@app.route('/')
def api_info():
    """API information endpoint"""
    return jsonify({
        'message': 'Welcome to Quizolia API v2.0',
        'documentation': '/swagger/',
        'version': '2.0.0',
        'endpoints': {
            'authentication': '/login, /signup, /logout',
            'user': '/user/profile',
            'quiz': '/categories, /questions',
            'refresh': '/refresh-token'
        }
    })

# Debug endpoint for authentication troubleshooting


@app.route('/debug/auth', methods=['GET'])
def debug_auth():
    """Debug endpoint to check authentication status"""
    access_token = request.cookies.get('access_token')
    refresh_token = request.cookies.get('refresh_token')

    debug_info = {
        'cookies_received': {
            'access_token': 'present' if access_token else 'missing',
            'refresh_token': 'present' if refresh_token else 'missing'
        },
        'headers': dict(request.headers),
        'origin': request.headers.get('Origin'),
        'user_agent': request.headers.get('User-Agent'),
        'environment': os.getenv('FLASK_ENV', 'development')
    }

    if access_token:
        payload = verify_token(access_token, 'access')
        debug_info['token_status'] = 'valid' if payload else 'invalid/expired'
        if payload:
            debug_info['user_id'] = payload.get('user_id')

    return jsonify({
        'success': True,
        'message': 'Debug information',
        'data': debug_info
    })

# Authentication Routes


@app.route('/signup', methods=['POST'])
def signup():
    """Register a new user"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['firstname', 'lastname', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'{field} is required',
                    'code': 'MISSING_FIELD'
                }), 400

        # Check if user already exists
        existing_user = Users.query.filter_by(email=data['email']).first()
        if existing_user:
            return jsonify({
                'success': False,
                'error': 'Email already registered',
                'code': 'EMAIL_EXISTS'
            }), 409

        # Create new user
        hashed_password = generate_password_hash(data['password'])
        new_user = Users(
            firstname=data['firstname'],
            lastname=data['lastname'],
            email=data['email'],
            password=hashed_password,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            last_login=datetime.utcnow()
        )

        db.session.add(new_user)
        db.session.commit()

        logger.info(f"New user registered: {data['email']}")

        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'data': {'user': new_user.to_dict()}
        }), 201

    except Exception as e:
        db.session.rollback()
        logger.error(f"Signup error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Registration failed',
            'code': 'REGISTRATION_ERROR'
        }), 500


@app.route('/login', methods=['POST'])
def login():
    """Authenticate user and set JWT cookies"""
    try:
        data = request.get_json()

        if not data.get('email') or not data.get('password'):
            return jsonify({
                'success': False,
                'error': 'Email and password are required',
                'code': 'MISSING_CREDENTIALS'
            }), 400

        # Find user with better error handling
        try:
            user = Users.query.filter_by(email=data['email']).first()
        except Exception as db_error:
            logger.error(f"Database error during login: {str(db_error)}")
            return jsonify({
                'success': False,
                'error': 'Database connection error',
                'code': 'DB_CONNECTION_ERROR'
            }), 500

        if not user or not check_password_hash(user.password, data['password']):
            return jsonify({
                'success': False,
                'error': 'Invalid email or password',
                'code': 'INVALID_CREDENTIALS'
            }), 401

        # Update last login
        try:
            user.last_login = datetime.utcnow()
            db.session.commit()
        except Exception as db_error:
            logger.error(
                f"Database error updating last login: {str(db_error)}")
            # Continue with login even if last_login update fails

        # Generate tokens
        access_token = generate_access_token(user.userId)
        refresh_token = generate_refresh_token(user.userId)

        # Create response
        response = make_response(jsonify({
            'success': True,
            'message': 'Login successful',
            'access_token': access_token,  # Also return in body for frontend
            'data': {'user': user.to_dict()}
        }))

        # Set HTTP-only cookies with proper cross-origin settings
        is_production = os.getenv('FLASK_ENV') == 'production'

        # For cross-origin requests, we need SameSite=None and Secure=True
        # But for local development, we use Lax and Secure=False
        cookie_settings = {
            'httponly': True,
            'max_age': int(app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds()),
            'samesite': 'None' if is_production else 'Lax',
            'secure': True if is_production else False,  # Must be True for SameSite=None
        }

        response.set_cookie('access_token', access_token, **cookie_settings)

        # Refresh token settings
        refresh_cookie_settings = cookie_settings.copy()
        refresh_cookie_settings['max_age'] = int(
            app.config['JWT_REFRESH_TOKEN_EXPIRES'].total_seconds())
        response.set_cookie('refresh_token', refresh_token,
                            **refresh_cookie_settings)

        logger.info(f"User logged in: {data['email']}")
        return response

    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Login failed',
            'code': 'LOGIN_ERROR'
        }), 500


@app.route('/logout', methods=['POST'])
def logout():
    """Logout user and clear JWT cookies"""
    response = make_response(jsonify({
        'success': True,
        'message': 'Logged out successfully'
    }))

    # Clear cookies
    response.set_cookie('access_token', '', expires=0, httponly=True)
    response.set_cookie('refresh_token', '', expires=0, httponly=True)

    return response


@app.route('/refresh-token', methods=['POST'])
def refresh_token():
    """Refresh access token using refresh token"""
    try:
        refresh_token = request.cookies.get('refresh_token')

        if not refresh_token:
            return jsonify({
                'success': False,
                'error': 'Refresh token missing',
                'code': 'REFRESH_TOKEN_MISSING'
            }), 401

        payload = verify_token(refresh_token, 'refresh')
        if not payload:
            return jsonify({
                'success': False,
                'error': 'Invalid or expired refresh token',
                'code': 'REFRESH_TOKEN_INVALID'
            }), 401

        # Get user
        user = Users.query.filter_by(userId=payload['user_id']).first()
        if not user:
            return jsonify({
                'success': False,
                'error': 'User not found',
                'code': 'USER_NOT_FOUND'
            }), 401

        # Generate new access token
        new_access_token = generate_access_token(user.userId)

        response = make_response(jsonify({
            'success': True,
            'message': 'Token refreshed successfully',
            'access_token': new_access_token
        }))

        # Set new access token cookie with proper cross-origin settings
        is_production = os.getenv('FLASK_ENV') == 'production'

        cookie_settings = {
            'httponly': True,
            'max_age': int(app.config['JWT_ACCESS_TOKEN_EXPIRES'].total_seconds()),
            'samesite': 'None' if is_production else 'Lax',
            'secure': True if is_production else False,
        }

        response.set_cookie(
            'access_token', new_access_token, **cookie_settings)

        return response

    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Token refresh failed',
            'code': 'REFRESH_ERROR'
        }), 500

# User Routes


@app.route('/user/profile', methods=['GET'])
@jwt_required
def get_user_profile():
    """Get current user profile"""
    return jsonify({
        'success': True,
        'message': 'Profile retrieved successfully',
        'data': {'user': request.current_user.to_dict()}
    })


@app.route('/user/profile', methods=['PUT'])
@jwt_required
def update_user_profile():
    """Update current user profile"""
    try:
        data = request.get_json()
        user = request.current_user

        # Update fields if provided
        if 'firstname' in data:
            user.firstname = data['firstname']
        if 'lastname' in data:
            user.lastname = data['lastname']
        if 'email' in data:
            # Check if email is already taken by another user
            existing_user = Users.query.filter_by(email=data['email']).first()
            if existing_user and existing_user.userId != user.userId:
                return jsonify({
                    'success': False,
                    'error': 'Email already in use',
                    'code': 'EMAIL_EXISTS'
                }), 409
            user.email = data['email']

        user.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Profile updated successfully',
            'data': {'user': user.to_dict()}
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"Profile update error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Profile update failed',
            'code': 'UPDATE_ERROR'
        }), 500

# Quiz Routes


@app.route('/categories', methods=['GET'])
@jwt_required
def get_categories():
    """Get quiz categories from external API"""
    try:
        # Use Open Trivia Database API
        response = requests.get(
            'https://opentdb.com/api.php?amount=50', timeout=10)

        if response.status_code == 200:
            data = response.json()
            categories = data.get('trivia_categories', [])

            # Format categories for frontend
            formatted_categories = []
            for cat in categories:
                formatted_categories.append({
                    'id': cat['id'],
                    'name': cat['name']
                })

            return jsonify({
                'success': True,
                'message': 'Categories retrieved successfully',
                'data': {'categories': formatted_categories}
            })
        else:
            # Fallback categories if API fails
            fallback_categories = [
                {'id': 9, 'name': 'General Knowledge'},
                {'id': 10, 'name': 'Entertainment: Books'},
                {'id': 11, 'name': 'Entertainment: Film'},
                {'id': 12, 'name': 'Entertainment: Music'},
                {'id': 17, 'name': 'Science & Nature'},
                {'id': 18, 'name': 'Science: Computers'},
                {'id': 19, 'name': 'Science: Mathematics'},
                {'id': 20, 'name': 'Mythology'},
                {'id': 21, 'name': 'Sports'},
                {'id': 22, 'name': 'Geography'},
                {'id': 23, 'name': 'History'},
                {'id': 24, 'name': 'Politics'},
                {'id': 25, 'name': 'Art'},
                {'id': 26, 'name': 'Celebrities'},
                {'id': 27, 'name': 'Animals'}
            ]

            return jsonify({
                'success': True,
                'message': 'Categories retrieved successfully (fallback)',
                'data': {'categories': fallback_categories}
            })

    except Exception as e:
        logger.error(f"Categories error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve categories',
            'code': 'CATEGORIES_ERROR'
        }), 500


@app.route('/questions', methods=['GET'])
@jwt_required
def get_questions():
    """Get quiz questions from external API"""
    try:
        # Get query parameters
        amount = request.args.get('amount', 10, type=int)
        category = request.args.get('category', type=int)
        difficulty = request.args.get('difficulty', 'medium')
        question_type = request.args.get('type', 'multiple')

        # Validate parameters
        if amount > 50:
            amount = 50
        if amount < 1:
            amount = 10

        # Build API URL
        api_url = f'https://opentdb.com/api.php?amount={amount}'

        if category:
            api_url += f'&category={category}'
        if difficulty in ['easy', 'medium', 'hard']:
            api_url += f'&difficulty={difficulty}'
        if question_type in ['multiple', 'boolean']:
            api_url += f'&type={question_type}'

        # Make API request
        response = requests.get(api_url, timeout=10)

        if response.status_code == 200:
            data = response.json()

            if data.get('response_code') == 0:
                questions = data.get('results', [])

                # Format questions for frontend
                formatted_questions = []
                for i, q in enumerate(questions):
                    # Decode HTML entities
                    import html
                    question_text = html.unescape(q['question'])
                    correct_answer = html.unescape(q['correct_answer'])
                    incorrect_answers = [html.unescape(
                        ans) for ans in q['incorrect_answers']]

                    # Combine and shuffle answers
                    all_answers = incorrect_answers + [correct_answer]
                    import random
                    random.shuffle(all_answers)

                    # Find correct answer index
                    correct_index = all_answers.index(correct_answer)

                    formatted_questions.append({
                        'id': i + 1,
                        'question': question_text,
                        'type': q['type'],
                        'difficulty': q['difficulty'],
                        'category': q['category'],
                        'options': all_answers,
                        'correct_answer': correct_index
                    })

                return jsonify({
                    'success': True,
                    'message': 'Questions retrieved successfully',
                    'data': {
                        'questions': formatted_questions,
                        'total': len(formatted_questions)
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'No questions available for the specified criteria',
                    'code': 'NO_QUESTIONS'
                }), 404
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to fetch questions from external API',
                'code': 'API_ERROR'
            }), 500

    except Exception as e:
        logger.error(f"Questions error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to retrieve questions',
            'code': 'QUESTIONS_ERROR'
        }), 500


@app.route('/submit-score', methods=['POST'])
@jwt_required
def submit_score():
    """Submit quiz score (placeholder for future implementation)"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['score', 'total_questions',
                           'category', 'difficulty']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'{field} is required',
                    'code': 'MISSING_FIELD'
                }), 400

        # For now, just return success
        # In the future, this would save to a scores table
        user = request.current_user

        logger.info(
            f"Score submitted by {user.email}: {data['score']}/{data['total_questions']}")

        return jsonify({
            'success': True,
            'message': 'Score submitted successfully',
            'data': {
                'user_id': user.userId,
                'score': data['score'],
                'total_questions': data['total_questions'],
                'percentage': round((data['score'] / data['total_questions']) * 100, 2)
            }
        })

    except Exception as e:
        logger.error(f"Submit score error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to submit score',
            'code': 'SUBMIT_SCORE_ERROR'
        }), 500

# Initialize database


def create_tables():
    """Create database tables"""
    try:
        with app.app_context():
            db.create_all()
            logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {str(e)}")


if __name__ == '__main__':
    # Create tables on startup
    create_tables()

    # Get port from environment (Render uses PORT environment variable)
    port = int(os.environ.get("PORT", 5000))
    debug = os.environ.get("FLASK_ENV") == "development"

    logger.info(f"Starting Quizolia API on port {port}")
    app.run(host="0.0.0.0", port=port, debug=debug)
