#!/usr/bin/env python3
"""
Test CORS and authentication configuration without requiring database
"""

import requests
import json

def test_cors_configuration(base_url="http://localhost:5000"):
    """Test CORS configuration for frontend integration"""
    
    print(f"🌐 Testing CORS configuration with {base_url}")
    print("=" * 60)
    
    # Frontend origins to test
    frontend_origins = [
        'http://localhost:3000',
        'http://localhost:5173',
        'https://quizolia-frontend.vercel.app',
        'https://quizolia-frontend-git-main-dohoudaniels-projects.vercel.app'
    ]
    
    for origin in frontend_origins:
        print(f"\n🔍 Testing origin: {origin}")
        
        # Test preflight request for categories endpoint
        headers = {
            'Origin': origin,
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        try:
            response = requests.options(f"{base_url}/categories", headers=headers)
            print(f"   Preflight Status: {response.status_code}")
            
            # Check CORS headers
            cors_headers = {}
            for header, value in response.headers.items():
                if 'access-control' in header.lower():
                    cors_headers[header] = value
            
            if cors_headers:
                print("   CORS Headers:")
                for header, value in cors_headers.items():
                    print(f"     {header}: {value}")
                
                # Check specific requirements
                if response.headers.get('Access-Control-Allow-Credentials') == 'true':
                    print("   ✅ Credentials allowed")
                else:
                    print("   ❌ Credentials not allowed")
                
                allowed_origin = response.headers.get('Access-Control-Allow-Origin')
                if allowed_origin == origin or allowed_origin == '*':
                    print("   ✅ Origin allowed")
                else:
                    print(f"   ❌ Origin not allowed (got: {allowed_origin})")
            else:
                print("   ❌ No CORS headers found")
                
        except Exception as e:
            print(f"   ❌ Error testing origin: {str(e)}")

def test_debug_endpoint(base_url="http://localhost:5000"):
    """Test the debug endpoint"""
    
    print(f"\n🔍 Testing debug endpoint")
    print("=" * 60)
    
    try:
        # Test without cookies
        response = requests.get(f"{base_url}/debug/auth")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Debug endpoint accessible")
            print(f"Environment: {data['data']['environment']}")
            print(f"Cookies received: {data['data']['cookies_received']}")
        else:
            print(f"❌ Debug endpoint failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing debug endpoint: {str(e)}")

def test_protected_endpoint_without_auth(base_url="http://localhost:5000"):
    """Test protected endpoint without authentication"""
    
    print(f"\n🔒 Testing protected endpoint without auth")
    print("=" * 60)
    
    frontend_origin = 'https://quizolia-frontend.vercel.app'
    
    try:
        headers = {'Origin': frontend_origin}
        response = requests.get(f"{base_url}/categories", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 401:
            data = response.json()
            print("✅ Correctly returns 401 for unauthenticated request")
            print(f"Error code: {data.get('code')}")
            print(f"Error message: {data.get('error')}")
            
            if 'debug' in data:
                print("Debug info:")
                for key, value in data['debug'].items():
                    print(f"  {key}: {value}")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing protected endpoint: {str(e)}")

def test_api_info(base_url="http://localhost:5000"):
    """Test API info endpoint"""
    
    print(f"\n📋 Testing API info endpoint")
    print("=" * 60)
    
    try:
        response = requests.get(f"{base_url}/")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API info accessible")
            print(f"Message: {data['message']}")
            print(f"Version: {data['version']}")
            print("Available endpoints:")
            for category, endpoints in data['endpoints'].items():
                print(f"  {category}: {endpoints}")
        else:
            print(f"❌ API info failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing API info: {str(e)}")

def test_health_check(base_url="http://localhost:5000"):
    """Test health check endpoint"""
    
    print(f"\n🏥 Testing health check")
    print("=" * 60)
    
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check accessible")
            print(f"Status: {data['status']}")
            print(f"Database: {data['database']}")
            print(f"Version: {data['version']}")
        else:
            print(f"❌ Health check failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing health check: {str(e)}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test CORS and auth configuration")
    parser.add_argument("--url", default="http://localhost:5000", 
                       help="API base URL (default: http://localhost:5000)")
    args = parser.parse_args()
    
    print("🧪 Testing Quizolia API Configuration")
    print("This test focuses on CORS and authentication setup")
    print("without requiring database operations.")
    
    test_health_check(args.url)
    test_api_info(args.url)
    test_debug_endpoint(args.url)
    test_protected_endpoint_without_auth(args.url)
    test_cors_configuration(args.url)
    
    print("\n" + "=" * 60)
    print("🎯 Configuration Test Complete!")
    print("\nKey points for frontend integration:")
    print("1. CORS should allow your frontend domain")
    print("2. Credentials should be allowed")
    print("3. Protected endpoints should return 401 without auth")
    print("4. Debug endpoint should show environment and cookie info")
    print("\nIf all tests pass, your backend is ready for frontend integration!")
